@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Sidebar variables */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Main dark theme using #28282B */
    --background: 0 0% 15.7%; /* #28282B */
    --foreground: 0 0% 95%;
    --card: 0 0% 15.7%; /* #28282B */
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 15.7%; /* #28282B */
    --popover-foreground: 0 0% 95%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 0 0% 15.7%;
    --secondary: 0 0% 20%; /* Slightly lighter than #28282B */
    --secondary-foreground: 0 0% 95%;
    --muted: 0 0% 20%; /* Slightly lighter than #28282B */
    --muted-foreground: 0 0% 70%;
    --accent: 0 0% 20%; /* Slightly lighter than #28282B */
    --accent-foreground: 0 0% 95%;
    --destructive: 0 62.8% 50%;
    --destructive-foreground: 0 0% 95%;
    --border: 0 0% 25%; /* Slightly lighter than #28282B for borders */
    --input: 0 0% 25%; /* Slightly lighter than #28282B for inputs */
    --ring: 217.2 91.2% 59.8%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Dark sidebar using #28282B */
    --sidebar-background: 0 0% 15.7%; /* #28282B */
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 0 0% 95%;
    --sidebar-primary-foreground: 0 0% 15.7%;
    --sidebar-accent: 0 0% 22%; /* Slightly lighter than #28282B for hover states */
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 0 0% 25%; /* Slightly lighter than #28282B for borders */
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar for dark mode using #28282B theme */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Dark mode specific styles using #28282B */
  .dark .bg-custom-dark {
    background-color: #28282b;
  }

  .dark .border-custom-dark {
    border-color: #3a3a3d; /* Slightly lighter than #28282B */
  }

  .dark .text-custom-light {
    color: #f5f5f5;
  }
}
