"use client"

import { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { auth, db } from '@/lib/supabase'
import { Database } from '@/types/database'
import { useAsyncErrorHandler } from '@/components/error-boundary'

type Profile = Database['public']['Tables']['profiles']['Row']

interface AuthContextType {
  user: User | null
  profile: Profile | null
  session: Session | null
  loading: boolean
  signUp: (email: string, password: string, metadata?: any) => Promise<any>
  signIn: (email: string, password: string) => Promise<any>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<any>
  updatePassword: (password: string) => Promise<any>
  updateProfile: (updates: Partial<Profile>) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const { executeWithRetry, handleError } = useAsyncErrorHandler()

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await auth.getSession()
      setSession(session)
      setUser(session?.user ?? null)
      
      if (session?.user) {
        await loadProfile(session.user.id)
      }
      
      setLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = auth.onAuthStateChange(async (event, session) => {
      setSession(session)
      setUser(session?.user ?? null)
      
      if (session?.user) {
        await loadProfile(session.user.id)
      } else {
        setProfile(null)
      }
      
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const loadProfile = async (userId: string) => {
    try {
      await executeWithRetry(async () => {
        const { data, error } = await db.profiles.get(userId)
        if (error) {
          throw new Error(`Failed to load profile: ${error.message}`)
        }
        setProfile(data)
      })
    } catch (error) {
      handleError(error as Error, { componentStack: 'AuthContext.loadProfile' })
      // Set profile to null on persistent failure
      setProfile(null)
    }
  }

  const signUp = async (email: string, password: string, metadata?: any) => {
    setLoading(true)
    try {
      const result = await executeWithRetry(
        () => auth.signUp(email, password, metadata),
        2, // Only retry once for auth operations
        2000
      )
      return result
    } catch (error) {
      handleError(error as Error, { componentStack: 'AuthContext.signUp' })
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    try {
      const result = await executeWithRetry(
        () => auth.signIn(email, password),
        2, // Only retry once for auth operations
        2000
      )
      return result
    } catch (error) {
      handleError(error as Error, { componentStack: 'AuthContext.signIn' })
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      await auth.signOut()
      setUser(null)
      setProfile(null)
      setSession(null)
    } finally {
      setLoading(false)
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const result = await auth.resetPassword(email)
      return result
    } catch (error) {
      console.error('Error resetting password:', error)
      throw error
    }
  }

  const updatePassword = async (password: string) => {
    if (!user) throw new Error('No user logged in')

    try {
      const result = await auth.updatePassword(password)
      return result
    } catch (error) {
      console.error('Error updating password:', error)
      throw error
    }
  }

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) throw new Error('No user logged in')
    
    try {
      await executeWithRetry(async () => {
        const { data, error } = await db.profiles.update(user.id, updates)
        if (error) throw new Error(`Failed to update profile: ${error.message}`)
        setProfile(data)
      })
    } catch (error) {
      handleError(error as Error, { componentStack: 'AuthContext.updateProfile' })
      throw error
    }
  }

  const value = {
    user,
    profile,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default AuthContext
