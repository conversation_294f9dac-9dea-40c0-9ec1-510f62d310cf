import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
}

if (!supabaseAnonKey) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
}

// Create Supabase client with TypeScript support
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
  },
  global: {
    headers: {
      'X-Client-Info': 'kuwait-subscription-tracker',
    },
  },
  db: {
    schema: 'public',
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
})

// Domain validation - configurable through environment variables
const ALLOWED_DOMAINS = process.env.NEXT_PUBLIC_ALLOWED_EMAIL_DOMAINS?.split(',').map(d => d.trim()) || ['advisory.kw.gt.com'];

const validateEmailDomain = (email: string): boolean => {
  const emailLower = email.toLowerCase();
  return ALLOWED_DOMAINS.some(domain => emailLower.endsWith(`@${domain.toLowerCase()}`));
};

const getDomainDisplayText = (): string => {
  return ALLOWED_DOMAINS.length === 1 ? ALLOWED_DOMAINS[0] : ALLOWED_DOMAINS.join(', ');
};

// Auth helpers
export const auth = {
  signUp: async (email: string, password: string, metadata?: any) => {
    // Validate domain before attempting signup
    if (!validateEmailDomain(email)) {
      return {
        data: { user: null, session: null },
        error: {
          message: `Registration is restricted to ${getDomainDisplayText()} email addresses only. Please use your company email address.`,
          status: 400,
          name: 'AuthError'
        }
      };
    }

    return await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    })
  },

  signIn: async (email: string, password: string) => {
    // Validate domain before attempting signin
    if (!validateEmailDomain(email)) {
      return {
        data: { user: null, session: null },
        error: {
          message: `Access is restricted to ${getDomainDisplayText()} email addresses only. Please use your company email address.`,
          status: 400,
          name: 'AuthError'
        }
      };
    }

    return await supabase.auth.signInWithPassword({
      email,
      password,
    })
  },

  signOut: async () => {
    return await supabase.auth.signOut()
  },

  resetPassword: async (email: string) => {
    // Validate domain before attempting password reset
    if (!validateEmailDomain(email)) {
      return {
        data: {},
        error: {
          message: `Password reset is only available for ${getDomainDisplayText()} email addresses. Please use your company email address.`,
          status: 400,
          name: 'AuthError'
        }
      };
    }

    return await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    })
  },

  updatePassword: async (password: string) => {
    return await supabase.auth.updateUser({
      password: password
    })
  },

  getUser: async () => {
    return await supabase.auth.getUser()
  },

  getSession: async () => {
    return await supabase.auth.getSession()
  },

  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback)
  },
}

// Database helpers
export const db = {
  // Profile operations
  profiles: {
    get: async (id: string) => {
      return await supabase
        .from('profiles')
        .select('*')
        .eq('id', id)
        .single()
    },

    update: async (id: string, updates: Partial<Database['public']['Tables']['profiles']['Update']>) => {
      return await supabase
        .from('profiles')
        .update(updates)
        .eq('id', id)
        .select()
        .single()
    },

    create: async (profile: Database['public']['Tables']['profiles']['Insert']) => {
      return await supabase
        .from('profiles')
        .insert(profile)
        .select()
        .single()
    },
  },

  // Business operations
  businesses: {
    getAll: async (userId: string) => {
      return await supabase
        .from('businesses')
        .select('*')
        .eq('owner_id', userId)
        .order('created_at', { ascending: false })
    },

    get: async (id: string) => {
      return await supabase
        .from('businesses')
        .select('*')
        .eq('id', id)
        .single()
    },

    create: async (business: Database['public']['Tables']['businesses']['Insert']) => {
      return await supabase
        .from('businesses')
        .insert(business)
        .select()
        .single()
    },

    update: async (id: string, updates: Database['public']['Tables']['businesses']['Update']) => {
      return await supabase
        .from('businesses')
        .update(updates)
        .eq('id', id)
        .select()
        .single()
    },

    delete: async (id: string) => {
      return await supabase
        .from('businesses')
        .delete()
        .eq('id', id)
    },
  },

  // Subscription operations
  subscriptions: {
    getAll: async (businessId: string) => {
      return await supabase
        .from('subscriptions')
        .select('*')
        .eq('business_id', businessId)
        .order('created_at', { ascending: false })
    },

    get: async (id: string) => {
      return await supabase
        .from('subscriptions')
        .select('*')
        .eq('id', id)
        .single()
    },

    create: async (subscription: Database['public']['Tables']['subscriptions']['Insert']) => {
      return await supabase
        .from('subscriptions')
        .insert(subscription)
        .select()
        .single()
    },

    update: async (id: string, updates: Database['public']['Tables']['subscriptions']['Update']) => {
      return await supabase
        .from('subscriptions')
        .update(updates)
        .eq('id', id)
        .select()
        .single()
    },

    delete: async (id: string) => {
      return await supabase
        .from('subscriptions')
        .delete()
        .eq('id', id)
    },
  },

  // Payment operations
  payments: {
    getAll: async (businessId: string) => {
      return await supabase
        .from('payments')
        .select(`
          *,
          subscriptions(name, category)
        `)
        .eq('business_id', businessId)
        .order('payment_date', { ascending: false })
    },

    get: async (id: string) => {
      return await supabase
        .from('payments')
        .select(`
          *,
          subscriptions(name, category)
        `)
        .eq('id', id)
        .single()
    },

    create: async (payment: Database['public']['Tables']['payments']['Insert']) => {
      return await supabase
        .from('payments')
        .insert(payment)
        .select()
        .single()
    },

    update: async (id: string, updates: Database['public']['Tables']['payments']['Update']) => {
      return await supabase
        .from('payments')
        .update(updates)
        .eq('id', id)
        .select()
        .single()
    },

    delete: async (id: string) => {
      return await supabase
        .from('payments')
        .delete()
        .eq('id', id)
    },
  },

  // Reimbursement operations
  reimbursements: {
    getAll: async (businessId: string) => {
      return await supabase
        .from('reimbursements')
        .select(`
          *,
          payments(amount, currency, payment_date, description)
        `)
        .eq('business_id', businessId)
        .order('submitted_date', { ascending: false })
    },

    get: async (id: string) => {
      return await supabase
        .from('reimbursements')
        .select(`
          *,
          payments(amount, currency, payment_date, description)
        `)
        .eq('id', id)
        .single()
    },

    create: async (reimbursement: Database['public']['Tables']['reimbursements']['Insert']) => {
      return await supabase
        .from('reimbursements')
        .insert(reimbursement)
        .select()
        .single()
    },

    update: async (id: string, updates: Database['public']['Tables']['reimbursements']['Update']) => {
      return await supabase
        .from('reimbursements')
        .update(updates)
        .eq('id', id)
        .select()
        .single()
    },

    delete: async (id: string) => {
      return await supabase
        .from('reimbursements')
        .delete()
        .eq('id', id)
    },
  },

  // Exchange rate operations
  exchangeRates: {
    getLatest: async (fromCurrency: string, toCurrency: string) => {
      return await supabase
        .from('exchange_rates')
        .select('*')
        .or(`and(base_currency.eq.${fromCurrency},target_currency.eq.${toCurrency}),and(from_currency.eq.${fromCurrency},to_currency.eq.${toCurrency})`)
        .order('date', { ascending: false })
        .limit(1)
        .single()
    },

    getByDate: async (fromCurrency: string, toCurrency: string, date: string) => {
      return await supabase
        .from('exchange_rates')
        .select('*')
        .or(`and(base_currency.eq.${fromCurrency},target_currency.eq.${toCurrency}),and(from_currency.eq.${fromCurrency},to_currency.eq.${toCurrency})`)
        .eq('date', date)
        .single()
    },

    create: async (rate: Database['public']['Tables']['exchange_rates']['Insert']) => {
      return await supabase
        .from('exchange_rates')
        .insert(rate)
        .select()
        .single()
    },
  },
}

// Real-time subscriptions
export const realtime = {
  subscribeToBusinesses: (userId: string, callback: (payload: any) => void) => {
    return supabase
      .channel('businesses')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'businesses',
          filter: `owner_id=eq.${userId}`,
        },
        callback
      )
      .subscribe()
  },

  subscribeToSubscriptions: (businessId: string, callback: (payload: any) => void) => {
    return supabase
      .channel('subscriptions')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'subscriptions',
          filter: `business_id=eq.${businessId}`,
        },
        callback
      )
      .subscribe()
  },

  subscribeToPayments: (businessId: string, callback: (payload: any) => void) => {
    return supabase
      .channel('payments')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'payments',
          filter: `business_id=eq.${businessId}`,
        },
        callback
      )
      .subscribe()
  },

  subscribeToReimbursements: (businessId: string, callback: (payload: any) => void) => {
    return supabase
      .channel('reimbursements')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'reimbursements',
          filter: `business_id=eq.${businessId}`,
        },
        callback
      )
      .subscribe()
  },
}

export default supabase
