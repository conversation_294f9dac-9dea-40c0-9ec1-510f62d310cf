// Comprehensive validation utilities with TypeScript support

import { z } from "zod"
import { 
  Currency, 
  BillingCycle, 
  SubscriptionStatus, 
  ReimbursementStatus,
  SUBSCRIPTION_CATEGORIES 
} from "@/types"

// Zod schemas for runtime validation
export const CurrencySchema = z.enum(["USD", "GBP", "KWD"])
export const BillingCycleSchema = z.enum(["monthly", "quarterly", "yearly"])
export const SubscriptionStatusSchema = z.enum(["active", "cancelled", "paused"])
export const ReimbursementStatusSchema = z.enum(["pending", "approved", "rejected"])
export const SubscriptionCategorySchema = z.enum(SUBSCRIPTION_CATEGORIES)

// Subscription validation schema
export const SubscriptionSchema = z.object({
  id: z.string().min(1, "ID is required"),
  name: z.string().min(1, "Subscription name is required").max(100, "Name too long"),
  category: SubscriptionCategorySchema,
  amount: z.number().positive("Amount must be positive"),
  currency: z.enum(["USD", "GBP"]),
  kwdAmount: z.number().positive("KWD amount must be positive"),
  exchangeRate: z.number().positive("Exchange rate must be positive"),
  billingCycle: BillingCycleSchema,
  nextPaymentDate: z.string().datetime("Invalid date format"),
  status: SubscriptionStatusSchema,
  description: z.string().max(500, "Description too long"),
  createdAt: z.string().datetime("Invalid date format"),
  lastUpdated: z.string().datetime("Invalid date format"),
})

// Subscription form validation schema
export const SubscriptionFormSchema = z.object({
  name: z.string().min(1, "Subscription name is required").max(100, "Name too long"),
  category: SubscriptionCategorySchema,
  amount: z.string().refine(
    (val) => !isNaN(Number(val)) && Number(val) > 0,
    "Amount must be a positive number"
  ),
  currency: z.enum(["USD", "GBP"]),
  billingCycle: BillingCycleSchema,
  nextPaymentDate: z.date(),
  status: SubscriptionStatusSchema,
  description: z.string().max(500, "Description too long"),
})

// Payment record validation schema
export const PaymentRecordSchema = z.object({
  id: z.string().min(1, "ID is required"),
  subscriptionName: z.string().min(1, "Subscription name is required"),
  originalAmount: z.number().positive("Amount must be positive"),
  originalCurrency: z.enum(["USD", "GBP"]),
  kwdAmount: z.number().positive("KWD amount must be positive"),
  exchangeRate: z.number().positive("Exchange rate must be positive"),
  conversionTimestamp: z.string().datetime("Invalid date format"),
  paymentDate: z.string().datetime("Invalid date format"),
  description: z.string().max(500, "Description too long"),
  receiptUrl: z.string().url("Invalid URL").optional(),
  autoCreateReimbursement: z.boolean(),
  createdAt: z.string().datetime("Invalid date format"),
})

// Payment form validation schema
export const PaymentFormSchema = z.object({
  subscriptionName: z.string().min(1, "Subscription name is required"),
  amount: z.string().refine(
    (val) => !isNaN(Number(val)) && Number(val) > 0,
    "Amount must be a positive number"
  ),
  currency: z.enum(["USD", "GBP"]),
  paymentDate: z.date(),
  description: z.string().max(500, "Description too long"),
  autoCreateReimbursement: z.boolean(),
})

// Reimbursement record validation schema
export const ReimbursementRecordSchema = z.object({
  id: z.string().min(1, "ID is required"),
  linkedPaymentId: z.string().min(1, "Linked payment ID is required"),
  kwdAmount: z.number().positive("KWD amount must be positive"),
  status: ReimbursementStatusSchema,
  requestDate: z.string().datetime("Invalid date format"),
  approvalDate: z.string().datetime("Invalid date format").optional(),
  notes: z.string().max(1000, "Notes too long"),
  createdAt: z.string().datetime("Invalid date format"),
})

// User profile validation schema
export const UserProfileSchema = z.object({
  firstName: z.string().min(1, "First name is required").max(50, "First name too long"),
  lastName: z.string().min(1, "Last name is required").max(50, "Last name too long"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(1, "Phone number is required").max(20, "Phone number too long"),
  address: z.string().max(200, "Address too long"),
  city: z.string().max(50, "City name too long"),
  country: z.string().max(50, "Country name too long"),
  bio: z.string().max(500, "Bio too long"),
  avatar: z.string().url("Invalid URL").nullable(),
  dateOfBirth: z.string().datetime("Invalid date format"),
  jobTitle: z.string().max(100, "Job title too long"),
  company: z.string().max(100, "Company name too long"),
  createdAt: z.string().datetime("Invalid date format"),
  lastUpdated: z.string().datetime("Invalid date format"),
})

// Currency rates validation schema
export const CurrencyRatesSchema = z.object({
  usdToKwd: z.number().positive("USD to KWD rate must be positive"),
  gbpToKwd: z.number().positive("GBP to KWD rate must be positive"),
  lastUpdated: z.string().datetime("Invalid date format"),
})

// App settings validation schema
export const AppSettingsSchema = z.object({
  notifications: z.object({
    emailNotifications: z.boolean(),
    pushNotifications: z.boolean(),
    paymentReminders: z.boolean(),
    weeklyReports: z.boolean(),
    currencyAlerts: z.boolean(),
  }),
  preferences: z.object({
    defaultCurrency: z.enum(["USD", "GBP"]),
    dateFormat: z.enum(["MM/DD/YYYY", "DD/MM/YYYY", "YYYY-MM-DD"]),
    timeFormat: z.enum(["12h", "24h"]),
    language: z.string().min(2, "Language code required"),
    autoRefreshRates: z.boolean(),
    compactView: z.boolean(),
  }),
  privacy: z.object({
    shareUsageData: z.boolean(),
    allowAnalytics: z.boolean(),
    showOnlineStatus: z.boolean(),
  }),
  lastUpdated: z.string().datetime("Invalid date format"),
})

// Validation helper functions
export const validateSubscription = (data: unknown) => {
  return SubscriptionSchema.safeParse(data)
}

export const validateSubscriptionForm = (data: unknown) => {
  return SubscriptionFormSchema.safeParse(data)
}

export const validatePaymentRecord = (data: unknown) => {
  return PaymentRecordSchema.safeParse(data)
}

export const validatePaymentForm = (data: unknown) => {
  return PaymentFormSchema.safeParse(data)
}

export const validateReimbursementRecord = (data: unknown) => {
  return ReimbursementRecordSchema.safeParse(data)
}

export const validateUserProfile = (data: unknown) => {
  return UserProfileSchema.safeParse(data)
}

export const validateCurrencyRates = (data: unknown) => {
  return CurrencyRatesSchema.safeParse(data)
}

export const validateAppSettings = (data: unknown) => {
  return AppSettingsSchema.safeParse(data)
}

// Custom validation functions
export const isValidCurrency = (currency: string): currency is Currency => {
  return ["USD", "GBP", "KWD"].includes(currency)
}

export const isValidBillingCycle = (cycle: string): cycle is BillingCycle => {
  return ["monthly", "quarterly", "yearly"].includes(cycle)
}

export const isValidSubscriptionStatus = (status: string): status is SubscriptionStatus => {
  return ["active", "cancelled", "paused"].includes(status)
}

export const isValidReimbursementStatus = (status: string): status is ReimbursementStatus => {
  return ["pending", "approved", "rejected"].includes(status)
}

// Form field validation helpers
export const validateEmail = (email: string): { isValid: boolean; error?: string } => {
  const result = z.string().email().safeParse(email)
  return {
    isValid: result.success,
    error: result.success ? undefined : "Invalid email address"
  }
}

export const validatePhone = (phone: string): { isValid: boolean; error?: string } => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, "")
  const isValid = phoneRegex.test(cleanPhone)
  
  return {
    isValid,
    error: isValid ? undefined : "Invalid phone number format"
  }
}

export const validateAmount = (amount: string): { isValid: boolean; error?: string } => {
  const num = parseFloat(amount)
  const isValid = !isNaN(num) && num > 0
  
  return {
    isValid,
    error: isValid ? undefined : "Amount must be a positive number"
  }
}

export const validateRequired = (value: string, fieldName: string): { isValid: boolean; error?: string } => {
  const isValid = value.trim().length > 0
  
  return {
    isValid,
    error: isValid ? undefined : `${fieldName} is required`
  }
}

// Batch validation for forms
export const validateFormData = <T>(
  data: T,
  schema: z.ZodSchema<T>
): { isValid: boolean; errors: Record<string, string>; data?: T } => {
  const result = schema.safeParse(data)
  
  if (result.success) {
    return {
      isValid: true,
      errors: {},
      data: result.data
    }
  }
  
  const errors: Record<string, string> = {}
  result.error.errors.forEach((error) => {
    const path = error.path.join(".")
    errors[path] = error.message
  })
  
  return {
    isValid: false,
    errors
  }
}

// Export all schemas for external use
export const ValidationSchemas = {
  Subscription: SubscriptionSchema,
  SubscriptionForm: SubscriptionFormSchema,
  PaymentRecord: PaymentRecordSchema,
  PaymentForm: PaymentFormSchema,
  ReimbursementRecord: ReimbursementRecordSchema,
  UserProfile: UserProfileSchema,
  CurrencyRates: CurrencyRatesSchema,
  AppSettings: AppSettingsSchema,
}
