const express = require('express');
const { z } = require('zod');
const { createClient } = require('@supabase/supabase-js');
const { asyncHandler, ValidationError } = require('../middleware/errorHandler');
const { authMiddleware } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

// Validation schemas
const signUpSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  phone: z.string().optional(),
  company: z.string().optional()
});

const signInSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required')
});

const updateProfileSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  phone: z.string().optional(),
  company: z.string().optional(),
  avatar_url: z.string().url().optional().or(z.literal(''))
});

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters')
});

const resetPasswordSchema = z.object({
  email: z.string().email('Invalid email format')
});

/**
 * @route POST /api/auth/signup
 * @desc Register a new user
 * @access Public
 */
router.post('/signup', asyncHandler(async (req, res) => {
  const validatedData = signUpSchema.parse(req.body);
  const { email, password, firstName, lastName, phone, company } = validatedData;

  // Sign up user with Supabase Auth
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        first_name: firstName,
        last_name: lastName,
        phone,
        company
      }
    }
  });

  if (authError) {
    logger.logAuth('signup_failed', null, email, false, { error: authError.message });
    throw new ValidationError(authError.message);
  }

  if (!authData.user) {
    throw new ValidationError('Failed to create user account');
  }

  // Create profile record
  const { error: profileError } = await supabase
    .from('profiles')
    .insert({
      id: authData.user.id,
      email: authData.user.email,
      first_name: firstName,
      last_name: lastName,
      phone,
      company
    });

  if (profileError) {
    logger.error('Failed to create user profile', { 
      userId: authData.user.id, 
      error: profileError.message 
    });
    // Note: User is created in auth but profile creation failed
    // This should be handled by a cleanup process or retry mechanism
  }

  logger.logAuth('signup_success', authData.user.id, email, true, {
    confirmationSent: !authData.session
  });

  res.status(201).json({
    message: authData.session 
      ? 'Account created successfully' 
      : 'Account created. Please check your email to confirm your account.',
    user: {
      id: authData.user.id,
      email: authData.user.email,
      firstName,
      lastName,
      phone,
      company,
      emailConfirmed: !!authData.session
    },
    session: authData.session
  });
}));

/**
 * @route POST /api/auth/signin
 * @desc Sign in user
 * @access Public
 */
router.post('/signin', asyncHandler(async (req, res) => {
  const validatedData = signInSchema.parse(req.body);
  const { email, password } = validatedData;

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });

  if (error) {
    logger.logAuth('signin_failed', null, email, false, { error: error.message });
    throw new ValidationError('Invalid email or password');
  }

  if (!data.user || !data.session) {
    throw new ValidationError('Authentication failed');
  }

  // Get user profile
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', data.user.id)
    .single();

  if (profileError) {
    logger.warn('Profile not found for user', { 
      userId: data.user.id, 
      error: profileError.message 
    });
  }

  logger.logAuth('signin_success', data.user.id, email, true);

  res.json({
    message: 'Signed in successfully',
    user: {
      id: data.user.id,
      email: data.user.email,
      firstName: profile?.first_name,
      lastName: profile?.last_name,
      phone: profile?.phone,
      company: profile?.company,
      avatar_url: profile?.avatar_url,
      emailConfirmed: !!data.user.email_confirmed_at
    },
    session: data.session
  });
}));

/**
 * @route POST /api/auth/signout
 * @desc Sign out user
 * @access Private
 */
router.post('/signout', authMiddleware, asyncHandler(async (req, res) => {
  const { error } = await req.supabase.auth.signOut();

  if (error) {
    logger.error('Signout failed', { userId: req.user.id, error: error.message });
    throw new ValidationError('Failed to sign out');
  }

  logger.logAuth('signout_success', req.user.id, req.user.email, true);

  res.json({
    message: 'Signed out successfully'
  });
}));

/**
 * @route GET /api/auth/me
 * @desc Get current user profile
 * @access Private
 */
router.get('/me', authMiddleware, asyncHandler(async (req, res) => {
  const { data: profile, error } = await req.supabase
    .from('profiles')
    .select('*')
    .eq('id', req.user.id)
    .single();

  if (error) {
    logger.error('Failed to fetch user profile', { 
      userId: req.user.id, 
      error: error.message 
    });
    throw new ValidationError('Failed to fetch user profile');
  }

  res.json({
    user: {
      id: profile.id,
      email: profile.email,
      firstName: profile.first_name,
      lastName: profile.last_name,
      phone: profile.phone,
      company: profile.company,
      avatar_url: profile.avatar_url,
      emailConfirmed: !!req.user.confirmed_at,
      createdAt: profile.created_at,
      updatedAt: profile.updated_at
    }
  });
}));

/**
 * @route PUT /api/auth/profile
 * @desc Update user profile
 * @access Private
 */
router.put('/profile', authMiddleware, asyncHandler(async (req, res) => {
  const validatedData = updateProfileSchema.parse(req.body);
  
  const updateData = {};
  if (validatedData.firstName !== undefined) updateData.first_name = validatedData.firstName;
  if (validatedData.lastName !== undefined) updateData.last_name = validatedData.lastName;
  if (validatedData.phone !== undefined) updateData.phone = validatedData.phone;
  if (validatedData.company !== undefined) updateData.company = validatedData.company;
  if (validatedData.avatar_url !== undefined) updateData.avatar_url = validatedData.avatar_url;

  const { data, error } = await req.supabase
    .from('profiles')
    .update(updateData)
    .eq('id', req.user.id)
    .select()
    .single();

  if (error) {
    logger.error('Failed to update user profile', { 
      userId: req.user.id, 
      error: error.message 
    });
    throw new ValidationError('Failed to update profile');
  }

  logger.info('Profile updated successfully', { userId: req.user.id });

  res.json({
    message: 'Profile updated successfully',
    user: {
      id: data.id,
      email: data.email,
      firstName: data.first_name,
      lastName: data.last_name,
      phone: data.phone,
      company: data.company,
      avatar_url: data.avatar_url,
      updatedAt: data.updated_at
    }
  });
}));

/**
 * @route POST /api/auth/change-password
 * @desc Change user password
 * @access Private
 */
router.post('/change-password', authMiddleware, asyncHandler(async (req, res) => {
  const validatedData = changePasswordSchema.parse(req.body);
  const { newPassword } = validatedData;

  const { error } = await req.supabase.auth.updateUser({
    password: newPassword
  });

  if (error) {
    logger.error('Failed to change password', { 
      userId: req.user.id, 
      error: error.message 
    });
    throw new ValidationError('Failed to change password');
  }

  logger.logAuth('password_changed', req.user.id, req.user.email, true);

  res.json({
    message: 'Password changed successfully'
  });
}));

/**
 * @route POST /api/auth/reset-password
 * @desc Request password reset
 * @access Public
 */
router.post('/reset-password', asyncHandler(async (req, res) => {
  const validatedData = resetPasswordSchema.parse(req.body);
  const { email } = validatedData;

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password`
  });

  if (error) {
    logger.error('Failed to send password reset email', { 
      email, 
      error: error.message 
    });
    // Don't reveal if email exists or not for security
  }

  logger.logAuth('password_reset_requested', null, email, !error);

  // Always return success to prevent email enumeration
  res.json({
    message: 'If an account with that email exists, a password reset link has been sent.'
  });
}));

/**
 * @route GET /api/auth/verify
 * @desc Verify JWT token
 * @access Private
 */
router.get('/verify', authMiddleware, asyncHandler(async (req, res) => {
  res.json({
    valid: true,
    user: {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role
    }
  });
}));

module.exports = router;