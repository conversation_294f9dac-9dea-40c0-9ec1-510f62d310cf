PRD_TITLE = "Subscription Tracker"

1. **Use Supabase** for all database and authentication
2. **Use the attached fawazahmed0/exchange-api implementation** (with primary & fallback URLs as per the MIGRATION guide) for currency conversion

---

## Build a Subscription Tracker Web App

### Project Overview

Create a full-stack web application to track subscription payments and reimbursements for multiple businesses based in Kuwait.
**Currency Flow**: Subscriptions are paid in USD/GBP (foreign currencies) but must be converted to KWD for local accounting and reimbursement processing.

---

## Core Business Context

### Currency Flow Requirements

* **Subscription Payments**: Made in USD or GBP
* **Business Location**: Kuwait-based, requiring KWD conversion for local accounting
* **Reimbursements**: Processed and paid in KWD
* **Reporting**: Show both original foreign amounts and KWD equivalents

---

## Technology Stack

* **Frontend**: React.js with TypeScript
* **Backend**: Node.js with Express
* **Database & Auth**: Supabase

  * **Database**: PostgreSQL tables hosted in Supabase
  * **Authentication**: Supabase Auth (email/password, JWT under the hood)
* **Currency API**: fawazahmed0/exchange-api via CDN + fallback
* **Edge Caching**: Supabase Edge Functions or Cloudflare Workers (optional)

---

## Currency API Integration

Use the attached fawazahmed0/exchange-api implementation method:

```javascript
// src/services/currencyService.ts

const API_BASE    = "https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1";
const FALLBACK    = "https://latest.currency-api.pages.dev/v1";

// Fetch JSON from primary, otherwise fallback
async function getExchangeRates(base: string): Promise<Record<string, number>> {
  const url = (endpoint: string) => `${endpoint}/currencies/${base.toLowerCase()}.json`;

  try {
    const res = await fetch(url(API_BASE));
    if (!res.ok) throw new Error("Primary API failed");
    return (await res.json())[base.toLowerCase()];
  } catch {
    const res = await fetch(url(FALLBACK));
    return (await res.json())[base.toLowerCase()];
  }
}

// Convert USD/GBP → KWD
export async function convertToKWD(
  amount: number,
  fromCurrency: "USD" | "GBP"
) {
  const rates = await getExchangeRates(fromCurrency);
  const rate  = rates["kwd"];
  return {
    originalAmount:   amount,
    originalCurrency: fromCurrency,
    kwdAmount:        amount * rate,
    exchangeRate:     rate,
    conversionDate:   new Date()
  };
}
```

> **Note:** This follows the migration guide for the new CDN endpoints and fallback mechanism .

---

## Data Models (Supabase Tables)

```sql
-- Users (managed by Supabase Auth + profile table)
CREATE TABLE profiles (
  id          uuid PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  email       text UNIQUE NOT NULL,
  name        text,
  created_at  timestamptz DEFAULT now(),
  last_login  timestamptz
);

-- Businesses
CREATE TABLE businesses (
  id                        uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  owner_id                  uuid REFERENCES profiles(id),
  name                      text NOT NULL,
  contact_email             text,
  reimbursement_timeline    int,            -- days
  base_currency             text DEFAULT 'KWD',
  created_at                timestamptz DEFAULT now()
);

-- Subscriptions
CREATE TABLE subscriptions (
  id                 uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  business_id        uuid REFERENCES businesses(id),
  service_name       text,
  provider           text,
  amount             numeric,              -- foreign currency
  currency           text CHECK (currency IN ('USD','GBP')),
  billing_frequency  text,                 -- e.g. 'monthly'
  next_due_date      date,
  category           text,
  is_active          boolean DEFAULT true
);

-- Payments
CREATE TABLE payments (
  id                   uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  subscription_id      uuid REFERENCES subscriptions(id),
  payment_date         date DEFAULT now(),
  amount_original      numeric,
  currency_original    text,
  exchange_rate        numeric,
  amount_kwd           numeric,
  conversion_date      timestamptz,
  rate_source          text,
  payment_method       text,
  receipt_url          text,
  notes                text,
  requires_reimbursement boolean DEFAULT true
);

-- Reimbursements
CREATE TABLE reimbursements (
  id                     uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  payment_id             uuid REFERENCES payments(id),
  business_id            uuid REFERENCES businesses(id),
  amount_kwd             numeric,
  request_date           date DEFAULT now(),
  expected_date          date,
  received_date          date,
  status                 text CHECK (status IN ('Pending','Approved','Paid')),
  reference_number       text,
  approved_by            uuid REFERENCES profiles(id),
  paid_by                uuid REFERENCES profiles(id),
  original_currency      text,
  original_amount        numeric
);

-- Exchange Rate Audit
CREATE TABLE exchange_rates (
  id            uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  date          date,
  base_currency text,
  kwd_rate      numeric,
  fetched_at    timestamptz,
  source        text
);
```

---

## Required API Endpoints

### Authentication (via Supabase Auth)

* `POST /api/auth/signup` → Supabase signup
* `POST /api/auth/login`  → Supabase login
* `POST /api/auth/logout` → Supabase logout
* `POST /api/auth/refresh`→ Supabase token refresh

### Business Management

* `GET    /api/businesses`
* `POST   /api/businesses`
* `PUT    /api/businesses/:id`
* `DELETE /api/businesses/:id`

### Subscription Management

* `GET    /api/subscriptions`
* `GET    /api/subscriptions/upcoming`
* `POST   /api/subscriptions`
* `PUT    /api/subscriptions/:id`
* `DELETE /api/subscriptions/:id`

### Payment Tracking

* `GET    /api/payments`
* `POST   /api/payments`
* `PUT    /api/payments/:id`
* `GET    /api/payments/:id/conversion-details`

### Reimbursement Management

* `GET    /api/reimbursements`
* `POST   /api/reimbursements`
* `PUT    /api/reimbursements/:id`
* `GET    /api/reimbursements/pending-kwd`

### Currency Management

* `GET  /api/currency/current-rates`
* `GET  /api/currency/convert-to-kwd/:amount/:from`
* `POST /api/currency/rates/refresh`
* `GET  /api/currency/rate-history`

### Reporting

* `GET /api/reports/monthly/:month/:year`
* `GET /api/reports/business/:businessId`
* `GET /api/reports/currency-impact`

---

## Frontend Pages & Features

1. **Dashboard**

   * Total active subscriptions (USD/GBP & KWD)
   * Upcoming payments (with live KWD rates)
   * Pending reimbursements (KWD)
   * Total spent & outstanding reimbursements

2. **Subscription Management**

   * Create/Edit with amount in USD/GBP + live KWD preview
   * List: “\$99.99 USD (≈ 30.70 KWD)”

3. **Payment Recording**

   * Auto-fetch KWD rate via `convertToKWD()`
   * Manual rate override
   * Auto-flag reimbursement

4. **Reimbursement Tracking**

   * Status dashboard (Pending → Approved → Paid)
   * Forms auto-populated from payment

5. **Currency Management**

   * Current Rates Ticker (USD→KWD, GBP→KWD)
   * Rate History Chart
   * Conversion Calculator (any USD/GBP to KWD)
   * Alerts on >5% rate changes

6. **Reports & Export**

   * Dual-currency CSV for accounting
   * Currency impact analysis

---

## Business Logic & Automation

1. **Subscription Setup** → live KWD preview
2. **Payment Recording** → `convertToKWD()` → audit in `exchange_rates` table
3. **Reimbursement** → auto-create from payment

* **Rate Fetching**: every 30 min during Kuwait business hours, cache 30 mins
* **Alerts**: email digests for upcoming payments, reimbursement reminders, rate change alerts

---

## UI/UX & Performance

* **Supabase** client in React for real-time updates
* Responsive design (desktop/mobile/tablet)
* HTTPS & input validation
* Database connection pooling via Supabase

---

## Deliverables

1. **Supabase-backed API** with Auth
2. **React/TypeScript SPA**
3. **Real-time KWD conversion** using attached exchange-api code
4. **Full audit trail** in Supabase
5. **Dual-currency reporting** & CSV export
6. **Automations**: reminders, alerts, email digests

This spec ensures Supabase handles all persistence and auth, while the attached fawazahmed0/exchange-api implementation (with primary + fallback URLs) powers accurate, auditable USD/GBP→KWD conversions.
