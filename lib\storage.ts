// Type-safe localStorage utilities

import { 
  Subscription, 
  PaymentRecord, 
  ReimbursementRecord, 
  UserProfile, 
  AppSettings, 
  CurrencyRates,
  STORAGE_KEYS 
} from "@/types"

// Generic localStorage utility with type safety
export class TypedStorage {
  static get<T>(key: string, defaultValue: T): T {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        return defaultValue
      }
      const item = localStorage.getItem(key)
      if (item === null) return defaultValue
      return JSON.parse(item) as T
    } catch (error) {
      console.error(`Error reading from localStorage key "${key}":`, error)
      return defaultValue
    }
  }

  static set<T>(key: string, value: T): void {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        return
      }
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error(`Error writing to localStorage key "${key}":`, error)
    }
  }

  static remove(key: string): void {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        return
      }
      localStorage.removeItem(key)
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error)
    }
  }

  static clear(): void {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('Error clearing localStorage:', error)
    }
  }
}

// Specific storage utilities for each data type
export const SubscriptionStorage = {
  get: (): Subscription[] => 
    TypedStorage.get(STORAGE_KEYS.SUBSCRIPTIONS, []),
  
  set: (subscriptions: Subscription[]): void => 
    TypedStorage.set(STORAGE_KEYS.SUBSCRIPTIONS, subscriptions),
  
  add: (subscription: Subscription): void => {
    const subscriptions = SubscriptionStorage.get()
    subscriptions.unshift(subscription)
    SubscriptionStorage.set(subscriptions)
  },
  
  update: (id: string, updatedSubscription: Subscription): void => {
    const subscriptions = SubscriptionStorage.get()
    const index = subscriptions.findIndex(s => s.id === id)
    if (index !== -1) {
      subscriptions[index] = updatedSubscription
      SubscriptionStorage.set(subscriptions)
    }
  },
  
  remove: (id: string): void => {
    const subscriptions = SubscriptionStorage.get()
    const filtered = subscriptions.filter(s => s.id !== id)
    SubscriptionStorage.set(filtered)
  }
}

export const PaymentStorage = {
  get: (): PaymentRecord[] => 
    TypedStorage.get(STORAGE_KEYS.PAYMENTS, []),
  
  set: (payments: PaymentRecord[]): void => 
    TypedStorage.set(STORAGE_KEYS.PAYMENTS, payments),
  
  add: (payment: PaymentRecord): void => {
    const payments = PaymentStorage.get()
    payments.unshift(payment)
    PaymentStorage.set(payments)
  }
}

export const ReimbursementStorage = {
  get: (): ReimbursementRecord[] => 
    TypedStorage.get(STORAGE_KEYS.REIMBURSEMENTS, []),
  
  set: (reimbursements: ReimbursementRecord[]): void => 
    TypedStorage.set(STORAGE_KEYS.REIMBURSEMENTS, reimbursements),
  
  add: (reimbursement: ReimbursementRecord): void => {
    const reimbursements = ReimbursementStorage.get()
    reimbursements.unshift(reimbursement)
    ReimbursementStorage.set(reimbursements)
  }
}

export const ProfileStorage = {
  get: (): UserProfile | null => {
    const profile = TypedStorage.get<UserProfile | null>(STORAGE_KEYS.USER_PROFILE, null)
    return profile
  },
  
  set: (profile: UserProfile): void => 
    TypedStorage.set(STORAGE_KEYS.USER_PROFILE, profile)
}

export const SettingsStorage = {
  get: (): AppSettings | null => {
    const settings = TypedStorage.get<AppSettings | null>(STORAGE_KEYS.APP_SETTINGS, null)
    return settings
  },
  
  set: (settings: AppSettings): void => 
    TypedStorage.set(STORAGE_KEYS.APP_SETTINGS, settings)
}

export const CurrencyRatesStorage = {
  get: (): { rates: CurrencyRates; timestamp: string } | null => {
    const cached = TypedStorage.get<{ rates: CurrencyRates; timestamp: string } | null>(
      STORAGE_KEYS.CURRENCY_RATES_CACHE, 
      null
    )
    return cached
  },
  
  set: (rates: CurrencyRates, timestamp: string): void => 
    TypedStorage.set(STORAGE_KEYS.CURRENCY_RATES_CACHE, { rates, timestamp })
}

// Utility functions for data validation
export const validateSubscription = (data: any): data is Subscription => {
  return (
    typeof data === 'object' &&
    typeof data.id === 'string' &&
    typeof data.name === 'string' &&
    typeof data.category === 'string' &&
    typeof data.amount === 'number' &&
    ['USD', 'GBP'].includes(data.currency) &&
    typeof data.kwdAmount === 'number' &&
    typeof data.exchangeRate === 'number' &&
    ['monthly', 'yearly', 'quarterly'].includes(data.billingCycle) &&
    typeof data.nextPaymentDate === 'string' &&
    ['active', 'cancelled', 'paused'].includes(data.status) &&
    typeof data.description === 'string' &&
    typeof data.createdAt === 'string' &&
    typeof data.lastUpdated === 'string'
  )
}

export const validatePaymentRecord = (data: any): data is PaymentRecord => {
  return (
    typeof data === 'object' &&
    typeof data.id === 'string' &&
    typeof data.subscriptionName === 'string' &&
    typeof data.originalAmount === 'number' &&
    ['USD', 'GBP'].includes(data.originalCurrency) &&
    typeof data.kwdAmount === 'number' &&
    typeof data.exchangeRate === 'number' &&
    typeof data.conversionTimestamp === 'string' &&
    typeof data.paymentDate === 'string' &&
    typeof data.description === 'string' &&
    typeof data.autoCreateReimbursement === 'boolean' &&
    typeof data.createdAt === 'string'
  )
}

export const validateReimbursementRecord = (data: any): data is ReimbursementRecord => {
  return (
    typeof data === 'object' &&
    typeof data.id === 'string' &&
    typeof data.linkedPaymentId === 'string' &&
    typeof data.kwdAmount === 'number' &&
    ['pending', 'approved', 'rejected'].includes(data.status) &&
    typeof data.requestDate === 'string' &&
    typeof data.notes === 'string' &&
    typeof data.createdAt === 'string'
  )
}

// Migration utilities for data format changes
export const migrateStorageData = (): void => {
  // Add migration logic here if data format changes in the future
  console.log('Storage data migration check completed')
}

// Export all storage utilities
export const Storage = {
  subscriptions: SubscriptionStorage,
  payments: PaymentStorage,
  reimbursements: ReimbursementStorage,
  profile: ProfileStorage,
  settings: SettingsStorage,
  currencyRates: CurrencyRatesStorage,
  typed: TypedStorage,
  validate: {
    subscription: validateSubscription,
    payment: validatePaymentRecord,
    reimbursement: validateReimbursementRecord,
  },
  migrate: migrateStorageData,
}
