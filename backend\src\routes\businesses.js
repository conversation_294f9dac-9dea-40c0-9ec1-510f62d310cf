const express = require('express');
const { z } = require('zod');
const { as<PERSON><PERSON><PERSON><PERSON>, ValidationError, NotFoundError, ForbiddenError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const createBusinessSchema = z.object({
  name: z.string().min(1, 'Business name is required').max(255, 'Business name too long'),
  description: z.string().optional(),
  industry: z.string().optional(),
  website: z.string().url().optional().or(z.literal('')),
  phone: z.string().optional(),
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    country: z.string().optional(),
    postal_code: z.string().optional()
  }).optional(),
  settings: z.object({
    default_currency: z.string().default('KWD'),
    timezone: z.string().default('Asia/Kuwait'),
    fiscal_year_start: z.string().default('01-01'),
    notification_preferences: z.object({
      email_notifications: z.boolean().default(true),
      currency_alerts: z.boolean().default(true),
      payment_reminders: z.boolean().default(true)
    }).optional()
  }).optional()
});

const updateBusinessSchema = z.object({
  name: z.string().min(1, 'Business name is required').max(255, 'Business name too long').optional(),
  description: z.string().optional(),
  industry: z.string().optional(),
  website: z.string().url().optional().or(z.literal('')),
  phone: z.string().optional(),
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    country: z.string().optional(),
    postal_code: z.string().optional()
  }).optional(),
  settings: z.object({
    default_currency: z.string().optional(),
    timezone: z.string().optional(),
    fiscal_year_start: z.string().optional(),
    notification_preferences: z.object({
      email_notifications: z.boolean().optional(),
      currency_alerts: z.boolean().optional(),
      payment_reminders: z.boolean().optional()
    }).optional()
  }).optional()
});

/**
 * Middleware to verify business ownership
 */
const verifyBusinessOwnership = asyncHandler(async (req, res, next) => {
  const businessId = req.params.id || req.params.businessId;
  
  if (!businessId) {
    throw new ValidationError('Business ID is required');
  }

  const { data: business, error } = await req.supabase
    .from('businesses')
    .select('id, owner_id, name')
    .eq('id', businessId)
    .single();

  if (error || !business) {
    throw new NotFoundError('Business not found');
  }

  if (business.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this business');
  }

  req.business = business;
  next();
});

/**
 * @route POST /api/businesses
 * @desc Create a new business
 * @access Private
 */
router.post('/', asyncHandler(async (req, res) => {
  const validatedData = createBusinessSchema.parse(req.body);
  
  const businessData = {
    owner_id: req.user.id,
    name: validatedData.name,
    description: validatedData.description,
    industry: validatedData.industry,
    website: validatedData.website,
    phone: validatedData.phone,
    address: validatedData.address,
    settings: {
      default_currency: 'KWD',
      timezone: 'Asia/Kuwait',
      fiscal_year_start: '01-01',
      notification_preferences: {
        email_notifications: true,
        currency_alerts: true,
        payment_reminders: true
      },
      ...validatedData.settings
    }
  };

  const { data: business, error } = await req.supabase
    .from('businesses')
    .insert(businessData)
    .select()
    .single();

  if (error) {
    logger.error('Failed to create business', { 
      userId: req.user.id, 
      error: error.message,
      businessData: { name: validatedData.name }
    });
    throw new ValidationError('Failed to create business');
  }

  logger.logBusiness('created', business.id, req.user.id, { name: business.name });

  res.status(201).json({
    message: 'Business created successfully',
    business: {
      id: business.id,
      name: business.name,
      description: business.description,
      industry: business.industry,
      website: business.website,
      phone: business.phone,
      address: business.address,
      settings: business.settings,
      createdAt: business.created_at,
      updatedAt: business.updated_at
    }
  });
}));

/**
 * @route GET /api/businesses
 * @desc Get all businesses for the current user
 * @access Private
 */
router.get('/', asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, search } = req.query;
  const offset = (page - 1) * limit;

  let query = req.supabase
    .from('businesses')
    .select('*', { count: 'exact' })
    .eq('owner_id', req.user.id)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (search) {
    query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,industry.ilike.%${search}%`);
  }

  const { data: businesses, error, count } = await query;

  if (error) {
    logger.error('Failed to fetch businesses', { 
      userId: req.user.id, 
      error: error.message 
    });
    throw new ValidationError('Failed to fetch businesses');
  }

  const totalPages = Math.ceil(count / limit);

  res.json({
    businesses: businesses.map(business => ({
      id: business.id,
      name: business.name,
      description: business.description,
      industry: business.industry,
      website: business.website,
      phone: business.phone,
      address: business.address,
      settings: business.settings,
      createdAt: business.created_at,
      updatedAt: business.updated_at
    })),
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: count,
      itemsPerPage: parseInt(limit),
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    }
  });
}));

/**
 * @route GET /api/businesses/:id
 * @desc Get a specific business
 * @access Private
 */
router.get('/:id', verifyBusinessOwnership, asyncHandler(async (req, res) => {
  const { data: business, error } = await req.supabase
    .from('businesses')
    .select('*')
    .eq('id', req.params.id)
    .single();

  if (error) {
    logger.error('Failed to fetch business', { 
      userId: req.user.id, 
      businessId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to fetch business');
  }

  res.json({
    business: {
      id: business.id,
      name: business.name,
      description: business.description,
      industry: business.industry,
      website: business.website,
      phone: business.phone,
      address: business.address,
      settings: business.settings,
      createdAt: business.created_at,
      updatedAt: business.updated_at
    }
  });
}));

/**
 * @route PUT /api/businesses/:id
 * @desc Update a business
 * @access Private
 */
router.put('/:id', verifyBusinessOwnership, asyncHandler(async (req, res) => {
  const validatedData = updateBusinessSchema.parse(req.body);
  
  // Remove undefined values
  const updateData = Object.fromEntries(
    Object.entries(validatedData).filter(([_, value]) => value !== undefined)
  );

  if (Object.keys(updateData).length === 0) {
    throw new ValidationError('No valid fields to update');
  }

  const { data: business, error } = await req.supabase
    .from('businesses')
    .update(updateData)
    .eq('id', req.params.id)
    .select()
    .single();

  if (error) {
    logger.error('Failed to update business', { 
      userId: req.user.id, 
      businessId: req.params.id,
      error: error.message,
      updateData
    });
    throw new ValidationError('Failed to update business');
  }

  logger.logBusiness('updated', business.id, req.user.id, { 
    name: business.name,
    updatedFields: Object.keys(updateData)
  });

  res.json({
    message: 'Business updated successfully',
    business: {
      id: business.id,
      name: business.name,
      description: business.description,
      industry: business.industry,
      website: business.website,
      phone: business.phone,
      address: business.address,
      settings: business.settings,
      createdAt: business.created_at,
      updatedAt: business.updated_at
    }
  });
}));

/**
 * @route DELETE /api/businesses/:id
 * @desc Delete a business
 * @access Private
 */
router.delete('/:id', verifyBusinessOwnership, asyncHandler(async (req, res) => {
  // Check if business has associated data
  const { data: subscriptions } = await req.supabase
    .from('subscriptions')
    .select('id')
    .eq('business_id', req.params.id)
    .limit(1);

  const { data: payments } = await req.supabase
    .from('payments')
    .select('id')
    .eq('business_id', req.params.id)
    .limit(1);

  const { data: reimbursements } = await req.supabase
    .from('reimbursements')
    .select('id')
    .eq('business_id', req.params.id)
    .limit(1);

  if (subscriptions?.length > 0 || payments?.length > 0 || reimbursements?.length > 0) {
    throw new ValidationError(
      'Cannot delete business with existing subscriptions, payments, or reimbursements. Please delete all associated data first.'
    );
  }

  const { error } = await req.supabase
    .from('businesses')
    .delete()
    .eq('id', req.params.id);

  if (error) {
    logger.error('Failed to delete business', { 
      userId: req.user.id, 
      businessId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to delete business');
  }

  logger.logBusiness('deleted', req.params.id, req.user.id, { 
    name: req.business.name 
  });

  res.json({
    message: 'Business deleted successfully'
  });
}));

/**
 * @route GET /api/businesses/:id/stats
 * @desc Get business statistics
 * @access Private
 */
router.get('/:id/stats', verifyBusinessOwnership, asyncHandler(async (req, res) => {
  const businessId = req.params.id;
  const { period = '30d' } = req.query;
  
  // Calculate date range based on period
  const now = new Date();
  let startDate;
  
  switch (period) {
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case '1y':
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  // Get subscription stats
  const { data: subscriptions, error: subError } = await req.supabase
    .from('subscriptions')
    .select('id, amount, currency, status, billing_cycle')
    .eq('business_id', businessId);

  // Get payment stats
  const { data: payments, error: payError } = await req.supabase
    .from('payments')
    .select('id, amount, currency, payment_date, status')
    .eq('business_id', businessId)
    .gte('payment_date', startDate.toISOString().split('T')[0]);

  // Get reimbursement stats
  const { data: reimbursements, error: reimbError } = await req.supabase
    .from('reimbursements')
    .select('id, amount, currency, status, expense_date')
    .eq('business_id', businessId)
    .gte('expense_date', startDate.toISOString().split('T')[0]);

  if (subError || payError || reimbError) {
    logger.error('Failed to fetch business stats', { 
      userId: req.user.id, 
      businessId,
      errors: { subError, payError, reimbError }
    });
    throw new ValidationError('Failed to fetch business statistics');
  }

  // Calculate statistics
  const stats = {
    subscriptions: {
      total: subscriptions?.length || 0,
      active: subscriptions?.filter(s => s.status === 'active').length || 0,
      totalValue: subscriptions?.reduce((sum, s) => sum + parseFloat(s.amount || 0), 0) || 0
    },
    payments: {
      total: payments?.length || 0,
      completed: payments?.filter(p => p.status === 'completed').length || 0,
      totalAmount: payments?.reduce((sum, p) => sum + parseFloat(p.amount || 0), 0) || 0
    },
    reimbursements: {
      total: reimbursements?.length || 0,
      pending: reimbursements?.filter(r => r.status === 'pending').length || 0,
      approved: reimbursements?.filter(r => r.status === 'approved').length || 0,
      totalAmount: reimbursements?.reduce((sum, r) => sum + parseFloat(r.amount || 0), 0) || 0
    },
    period: {
      startDate: startDate.toISOString().split('T')[0],
      endDate: now.toISOString().split('T')[0],
      days: Math.ceil((now - startDate) / (1000 * 60 * 60 * 24))
    }
  };

  res.json({ stats });
}));

module.exports = router;