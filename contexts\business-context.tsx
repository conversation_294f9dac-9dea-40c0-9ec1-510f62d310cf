"use client"

import { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { db } from '@/lib/supabase'
import { Database } from '@/types/database'

type Business = Database['public']['Tables']['businesses']['Row']

interface BusinessContextType {
  businesses: Business[]
  currentBusiness: Business | null
  loading: boolean
  error: string | null
  setCurrentBusiness: (business: Business | null) => void
  createBusiness: (business: Omit<Business, 'id' | 'owner_id' | 'created_at' | 'updated_at'>) => Promise<Business>
  updateBusiness: (id: string, updates: Partial<Business>) => Promise<Business>
  deleteBusiness: (id: string) => Promise<void>
  refreshBusinesses: () => Promise<void>
}

const BusinessContext = createContext<BusinessContextType | undefined>(undefined)

export function BusinessProvider({ children }: { children: React.ReactNode }) {
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [currentBusiness, setCurrentBusiness] = useState<Business | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const { user } = useAuth()

  useEffect(() => {
    if (user) {
      loadBusinesses()
    } else {
      setBusinesses([])
      setCurrentBusiness(null)
      setLoading(false)
    }
  }, [user])

  const loadBusinesses = async () => {
    if (!user) return

    try {
      setLoading(true)
      setError(null)
      
      const { data, error } = await db.businesses.getAll(user.id)
      
      if (error) {
        setError(error.message)
        return
      }

      setBusinesses(data || [])
      
      // Set current business to first one if none selected
      if (!currentBusiness && data && data.length > 0) {
        setCurrentBusiness(data[0])
      }
    } catch (err) {
      setError('Failed to load businesses')
      console.error('Error loading businesses:', err)
    } finally {
      setLoading(false)
    }
  }

  const createBusiness = async (businessData: Omit<Business, 'id' | 'owner_id' | 'created_at' | 'updated_at'>) => {
    if (!user) throw new Error('No user logged in')

    try {
      const { data, error } = await db.businesses.create({
        ...businessData,
        owner_id: user.id,
      })

      if (error) throw error

      setBusinesses(prev => [data, ...prev])
      
      // Set as current business if it's the first one
      if (businesses.length === 0) {
        setCurrentBusiness(data)
      }

      return data
    } catch (err) {
      console.error('Error creating business:', err)
      throw err
    }
  }

  const updateBusiness = async (id: string, updates: Partial<Business>) => {
    try {
      const { data, error } = await db.businesses.update(id, updates)

      if (error) throw error

      setBusinesses(prev => 
        prev.map(business => 
          business.id === id ? data : business
        )
      )

      if (currentBusiness?.id === id) {
        setCurrentBusiness(data)
      }

      return data
    } catch (err) {
      console.error('Error updating business:', err)
      throw err
    }
  }

  const deleteBusiness = async (id: string) => {
    try {
      const { error } = await db.businesses.delete(id)

      if (error) throw error

      setBusinesses(prev => prev.filter(business => business.id !== id))

      if (currentBusiness?.id === id) {
        const remainingBusinesses = businesses.filter(b => b.id !== id)
        setCurrentBusiness(remainingBusinesses.length > 0 ? remainingBusinesses[0] : null)
      }
    } catch (err) {
      console.error('Error deleting business:', err)
      throw err
    }
  }

  const refreshBusinesses = async () => {
    await loadBusinesses()
  }

  const value = {
    businesses,
    currentBusiness,
    loading,
    error,
    setCurrentBusiness,
    createBusiness,
    updateBusiness,
    deleteBusiness,
    refreshBusinesses,
  }

  return (
    <BusinessContext.Provider value={value}>
      {children}
    </BusinessContext.Provider>
  )
}

export function useBusiness() {
  const context = useContext(BusinessContext)
  if (context === undefined) {
    throw new Error('useBusiness must be used within a BusinessProvider')
  }
  return context
}

export default BusinessContext
