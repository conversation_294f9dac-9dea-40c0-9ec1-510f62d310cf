"use client"

import { useState, useEffect } from 'react'
import { db, realtime } from '@/lib/supabase'
import { useBusiness } from '@/contexts/business-context'
import { Database } from '@/types/database'

type Payment = Database['public']['Tables']['payments']['Row']
type PaymentInsert = Database['public']['Tables']['payments']['Insert']
type PaymentUpdate = Database['public']['Tables']['payments']['Update']

interface PaymentWithSubscription extends Payment {
  subscriptions?: {
    name: string
    category: string | null
  } | null
}

export function usePayments() {
  const [payments, setPayments] = useState<PaymentWithSubscription[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const { currentBusiness } = useBusiness()

  useEffect(() => {
    if (currentBusiness) {
      loadPayments()
      
      // Set up real-time subscription
      const subscription = realtime.subscribeToPayments(
        currentBusiness.id,
        (payload) => {
          console.log('Real-time payment update:', payload)
          
          switch (payload.eventType) {
            case 'INSERT':
              // For real-time updates, we might not have the subscription data
              // so we should reload to get complete data
              loadPayments()
              break
            case 'UPDATE':
              setPayments(prev => 
                prev.map(payment => 
                  payment.id === payload.new.id ? { ...payment, ...payload.new } : payment
                )
              )
              break
            case 'DELETE':
              setPayments(prev => 
                prev.filter(payment => payment.id !== payload.old.id)
              )
              break
          }
        }
      )

      return () => {
        subscription.unsubscribe()
      }
    } else {
      setPayments([])
      setLoading(false)
    }
  }, [currentBusiness])

  const loadPayments = async () => {
    if (!currentBusiness) return

    try {
      setLoading(true)
      setError(null)
      
      const { data, error } = await db.payments.getAll(currentBusiness.id)
      
      if (error) {
        setError(error.message)
        return
      }

      setPayments(data || [])
    } catch (err) {
      setError('Failed to load payments')
      console.error('Error loading payments:', err)
    } finally {
      setLoading(false)
    }
  }

  const createPayment = async (paymentData: Omit<PaymentInsert, 'business_id'>) => {
    if (!currentBusiness) throw new Error('No business selected')

    try {
      const { data, error } = await db.payments.create({
        ...paymentData,
        business_id: currentBusiness.id,
      })

      if (error) throw error

      // Reload to get complete data with subscription info
      await loadPayments()

      return data
    } catch (err) {
      console.error('Error creating payment:', err)
      throw err
    }
  }

  const updatePayment = async (id: string, updates: PaymentUpdate) => {
    try {
      const { data, error } = await db.payments.update(id, updates)

      if (error) throw error

      // Update local state
      setPayments(prev => 
        prev.map(payment => 
          payment.id === id ? { ...payment, ...data } : payment
        )
      )

      return data
    } catch (err) {
      console.error('Error updating payment:', err)
      throw err
    }
  }

  const deletePayment = async (id: string) => {
    try {
      const { error } = await db.payments.delete(id)

      if (error) throw error

      setPayments(prev => prev.filter(payment => payment.id !== id))
    } catch (err) {
      console.error('Error deleting payment:', err)
      throw err
    }
  }

  const getPaymentsByDateRange = (startDate: Date, endDate: Date) => {
    return payments.filter(payment => {
      const paymentDate = new Date(payment.payment_date)
      return paymentDate >= startDate && paymentDate <= endDate
    })
  }

  const getTotalSpentThisMonth = () => {
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)
    
    return getPaymentsByDateRange(startOfMonth, endOfMonth)
      .filter(payment => payment.status === 'completed')
      .reduce((total, payment) => total + (payment.kwd_amount || payment.amount), 0)
  }

  const getTotalSpentByCategory = () => {
    const categoryTotals: Record<string, number> = {}
    
    payments
      .filter(payment => payment.status === 'completed')
      .forEach(payment => {
        const category = payment.subscriptions?.category || 'Other'
        const amount = payment.kwd_amount || payment.amount
        categoryTotals[category] = (categoryTotals[category] || 0) + amount
      })
    
    return categoryTotals
  }

  const getMonthlyTrends = (months: number = 6) => {
    const trends = []
    const now = new Date()
    
    for (let i = months - 1; i >= 0; i--) {
      const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const startOfMonth = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1)
      const endOfMonth = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0)
      
      const monthPayments = getPaymentsByDateRange(startOfMonth, endOfMonth)
        .filter(payment => payment.status === 'completed')
      
      const total = monthPayments.reduce((sum, payment) => 
        sum + (payment.kwd_amount || payment.amount), 0
      )
      
      trends.push({
        month: monthDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        total,
        count: monthPayments.length,
        date: monthDate,
      })
    }
    
    return trends
  }

  const refreshPayments = async () => {
    await loadPayments()
  }

  return {
    payments,
    loading,
    error,
    createPayment,
    updatePayment,
    deletePayment,
    getPaymentsByDateRange,
    getTotalSpentThisMonth,
    getTotalSpentByCategory,
    getMonthlyTrends,
    refreshPayments,
  }
}
