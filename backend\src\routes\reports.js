const express = require('express');
const { z } = require('zod');
const { async<PERSON><PERSON><PERSON>, ValidationError, NotFoundError, ForbiddenError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const reportQuerySchema = z.object({
  business_id: z.string().uuid('Invalid business ID'),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date must be in YYYY-MM-DD format'),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date must be in YYYY-MM-DD format'),
  report_type: z.enum(['summary', 'detailed', 'payments', 'subscriptions', 'reimbursements']).default('summary'),
  group_by: z.enum(['day', 'week', 'month', 'quarter', 'year']).optional(),
  currency: z.string().length(3, 'Currency must be 3 characters').default('KWD')
});

const csvExportSchema = z.object({
  business_id: z.string().uuid('Invalid business ID'),
  export_type: z.enum(['payments', 'subscriptions', 'reimbursements', 'all']),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date must be in YYYY-MM-DD format').optional(),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date must be in YYYY-MM-DD format').optional(),
  include_fields: z.array(z.string()).optional()
});

/**
 * Middleware to verify business ownership
 */
const verifyBusinessOwnership = asyncHandler(async (req, res, next) => {
  const businessId = req.query.business_id || req.body.business_id;
  
  if (!businessId) {
    throw new ValidationError('Business ID is required');
  }

  const { data: business, error } = await req.supabase
    .from('businesses')
    .select('id, owner_id, name')
    .eq('id', businessId)
    .single();

  if (error || !business) {
    throw new NotFoundError('Business not found');
  }

  if (business.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this business');
  }

  req.business = business;
  next();
});

/**
 * Helper function to convert data to CSV format
 */
function convertToCSV(data, headers) {
  if (!data || data.length === 0) {
    return headers.join(',') + '\n';
  }
  
  const csvHeaders = headers.join(',');
  const csvRows = data.map(row => {
    return headers.map(header => {
      const value = row[header];
      // Escape commas and quotes in CSV
      if (value === null || value === undefined) {
        return '';
      }
      const stringValue = String(value);
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    }).join(',');
  });
  
  return csvHeaders + '\n' + csvRows.join('\n');
}

/**
 * Helper function to get exchange rate for currency conversion
 */
async function getExchangeRate(supabase, fromCurrency, toCurrency, date) {
  if (fromCurrency === toCurrency) {
    return 1;
  }
  
  const { data: rate } = await supabase
    .from('exchange_rates')
    .select('rate')
    .eq('base_currency', fromCurrency)
    .eq('target_currency', toCurrency)
    .eq('date', date)
    .single();
  
  if (rate) {
    return parseFloat(rate.rate);
  }
  
  // Try most recent rate
  const { data: recentRate } = await supabase
    .from('exchange_rates')
    .select('rate')
    .eq('base_currency', fromCurrency)
    .eq('target_currency', toCurrency)
    .order('date', { ascending: false })
    .limit(1)
    .single();
  
  return recentRate ? parseFloat(recentRate.rate) : 1;
}

/**
 * @route GET /api/reports/summary
 * @desc Get business summary report
 * @access Private
 */
router.get('/summary', verifyBusinessOwnership, asyncHandler(async (req, res) => {
  const validatedData = reportQuerySchema.parse(req.query);
  const { business_id, start_date, end_date, currency, group_by } = validatedData;
  
  // Validate date range
  const startDate = new Date(start_date);
  const endDate = new Date(end_date);
  
  if (startDate > endDate) {
    throw new ValidationError('Start date must be before end date');
  }
  
  // Get payments data
  const { data: payments, error: paymentsError } = await req.supabase
    .from('payments')
    .select('amount, currency, payment_date, status, category, payment_method')
    .eq('business_id', business_id)
    .gte('payment_date', start_date)
    .lte('payment_date', end_date);
  
  if (paymentsError) {
    logger.error('Failed to fetch payments for report', { 
      userId: req.user.id, 
      businessId: business_id,
      error: paymentsError.message 
    });
    throw new ValidationError('Failed to fetch payments data');
  }
  
  // Get subscriptions data
  const { data: subscriptions, error: subscriptionsError } = await req.supabase
    .from('subscriptions')
    .select('amount, currency, billing_cycle, status, created_at')
    .eq('business_id', business_id)
    .gte('created_at', start_date)
    .lte('created_at', end_date);
  
  if (subscriptionsError) {
    logger.error('Failed to fetch subscriptions for report', { 
      userId: req.user.id, 
      businessId: business_id,
      error: subscriptionsError.message 
    });
    throw new ValidationError('Failed to fetch subscriptions data');
  }
  
  // Get reimbursements data
  const { data: reimbursements, error: reimbursementsError } = await req.supabase
    .from('reimbursements')
    .select('amount, currency, expense_date, status, category, employee_name')
    .eq('business_id', business_id)
    .gte('expense_date', start_date)
    .lte('expense_date', end_date);
  
  if (reimbursementsError) {
    logger.error('Failed to fetch reimbursements for report', { 
      userId: req.user.id, 
      businessId: business_id,
      error: reimbursementsError.message 
    });
    throw new ValidationError('Failed to fetch reimbursements data');
  }
  
  // Convert amounts to target currency if needed
  const convertedPayments = await Promise.all(
    (payments || []).map(async (payment) => {
      if (payment.currency !== currency) {
        const rate = await getExchangeRate(req.supabase, payment.currency, currency, payment.payment_date);
        return { ...payment, convertedAmount: payment.amount * rate };
      }
      return { ...payment, convertedAmount: payment.amount };
    })
  );
  
  const convertedReimbursements = await Promise.all(
    (reimbursements || []).map(async (reimbursement) => {
      if (reimbursement.currency !== currency) {
        const rate = await getExchangeRate(req.supabase, reimbursement.currency, currency, reimbursement.expense_date);
        return { ...reimbursement, convertedAmount: reimbursement.amount * rate };
      }
      return { ...reimbursement, convertedAmount: reimbursement.amount };
    })
  );
  
  // Calculate summary statistics
  const summary = {
    period: {
      startDate: start_date,
      endDate: end_date,
      days: Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1
    },
    currency,
    payments: {
      total: convertedPayments.reduce((sum, p) => sum + p.convertedAmount, 0),
      count: convertedPayments.length,
      completed: convertedPayments.filter(p => p.status === 'completed').length,
      pending: convertedPayments.filter(p => p.status === 'pending').length,
      failed: convertedPayments.filter(p => p.status === 'failed').length,
      byCategory: {},
      byPaymentMethod: {}
    },
    subscriptions: {
      total: (subscriptions || []).reduce((sum, s) => sum + s.amount, 0),
      count: (subscriptions || []).length,
      active: (subscriptions || []).filter(s => s.status === 'active').length,
      paused: (subscriptions || []).filter(s => s.status === 'paused').length,
      cancelled: (subscriptions || []).filter(s => s.status === 'cancelled').length,
      byBillingCycle: {}
    },
    reimbursements: {
      total: convertedReimbursements.reduce((sum, r) => sum + r.convertedAmount, 0),
      count: convertedReimbursements.length,
      pending: convertedReimbursements.filter(r => r.status === 'pending').length,
      approved: convertedReimbursements.filter(r => r.status === 'approved').length,
      paid: convertedReimbursements.filter(r => r.status === 'paid').length,
      rejected: convertedReimbursements.filter(r => r.status === 'rejected').length,
      byCategory: {},
      byEmployee: {}
    }
  };
  
  // Group payments by category and payment method
  convertedPayments.forEach(payment => {
    const category = payment.category || 'Uncategorized';
    const method = payment.payment_method || 'Unknown';
    
    if (!summary.payments.byCategory[category]) {
      summary.payments.byCategory[category] = { count: 0, amount: 0 };
    }
    summary.payments.byCategory[category].count++;
    summary.payments.byCategory[category].amount += payment.convertedAmount;
    
    if (!summary.payments.byPaymentMethod[method]) {
      summary.payments.byPaymentMethod[method] = { count: 0, amount: 0 };
    }
    summary.payments.byPaymentMethod[method].count++;
    summary.payments.byPaymentMethod[method].amount += payment.convertedAmount;
  });
  
  // Group subscriptions by billing cycle
  (subscriptions || []).forEach(subscription => {
    const cycle = subscription.billing_cycle || 'Unknown';
    if (!summary.subscriptions.byBillingCycle[cycle]) {
      summary.subscriptions.byBillingCycle[cycle] = { count: 0, amount: 0 };
    }
    summary.subscriptions.byBillingCycle[cycle].count++;
    summary.subscriptions.byBillingCycle[cycle].amount += subscription.amount;
  });
  
  // Group reimbursements by category and employee
  convertedReimbursements.forEach(reimbursement => {
    const category = reimbursement.category || 'Uncategorized';
    const employee = reimbursement.employee_name || 'Unknown';
    
    if (!summary.reimbursements.byCategory[category]) {
      summary.reimbursements.byCategory[category] = { count: 0, amount: 0 };
    }
    summary.reimbursements.byCategory[category].count++;
    summary.reimbursements.byCategory[category].amount += reimbursement.convertedAmount;
    
    if (!summary.reimbursements.byEmployee[employee]) {
      summary.reimbursements.byEmployee[employee] = { count: 0, amount: 0 };
    }
    summary.reimbursements.byEmployee[employee].count++;
    summary.reimbursements.byEmployee[employee].amount += reimbursement.convertedAmount;
  });
  
  // Add time-based grouping if requested
  if (group_by) {
    summary.timeGrouped = {
      payments: {},
      reimbursements: {}
    };
    
    // Group payments by time period
    convertedPayments.forEach(payment => {
      const date = new Date(payment.payment_date);
      let groupKey;
      
      switch (group_by) {
        case 'day':
          groupKey = payment.payment_date;
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          groupKey = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
          groupKey = payment.payment_date.substring(0, 7); // YYYY-MM
          break;
        case 'quarter':
          const quarter = Math.floor(date.getMonth() / 3) + 1;
          groupKey = `${date.getFullYear()}-Q${quarter}`;
          break;
        case 'year':
          groupKey = payment.payment_date.substring(0, 4); // YYYY
          break;
      }
      
      if (!summary.timeGrouped.payments[groupKey]) {
        summary.timeGrouped.payments[groupKey] = { count: 0, amount: 0 };
      }
      summary.timeGrouped.payments[groupKey].count++;
      summary.timeGrouped.payments[groupKey].amount += payment.convertedAmount;
    });
    
    // Group reimbursements by time period
    convertedReimbursements.forEach(reimbursement => {
      const date = new Date(reimbursement.expense_date);
      let groupKey;
      
      switch (group_by) {
        case 'day':
          groupKey = reimbursement.expense_date;
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          groupKey = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
          groupKey = reimbursement.expense_date.substring(0, 7); // YYYY-MM
          break;
        case 'quarter':
          const quarter = Math.floor(date.getMonth() / 3) + 1;
          groupKey = `${date.getFullYear()}-Q${quarter}`;
          break;
        case 'year':
          groupKey = reimbursement.expense_date.substring(0, 4); // YYYY
          break;
      }
      
      if (!summary.timeGrouped.reimbursements[groupKey]) {
        summary.timeGrouped.reimbursements[groupKey] = { count: 0, amount: 0 };
      }
      summary.timeGrouped.reimbursements[groupKey].count++;
      summary.timeGrouped.reimbursements[groupKey].amount += reimbursement.convertedAmount;
    });
  }
  
  logger.info('Summary report generated', {
    userId: req.user.id,
    businessId: business_id,
    startDate: start_date,
    endDate: end_date,
    currency,
    paymentsCount: convertedPayments.length,
    reimbursementsCount: convertedReimbursements.length,
    subscriptionsCount: (subscriptions || []).length
  });
  
  res.json({ summary });
}));

/**
 * @route POST /api/reports/export/csv
 * @desc Export data as CSV
 * @access Private
 */
router.post('/export/csv', verifyBusinessOwnership, asyncHandler(async (req, res) => {
  const validatedData = csvExportSchema.parse(req.body);
  const { business_id, export_type, start_date, end_date, include_fields } = validatedData;
  
  let csvData = '';
  let filename = `${req.business.name.replace(/[^a-zA-Z0-9]/g, '_')}_${export_type}_${new Date().toISOString().split('T')[0]}.csv`;
  
  try {
    switch (export_type) {
      case 'payments': {
        let query = req.supabase
          .from('payments')
          .select(`
            id,
            amount,
            currency,
            original_amount,
            original_currency,
            exchange_rate,
            payment_date,
            payment_method,
            description,
            category,
            vendor,
            reference_number,
            receipt_url,
            notes,
            tags,
            status,
            created_at,
            subscriptions(name)
          `)
          .eq('business_id', business_id)
          .order('payment_date', { ascending: false });
        
        if (start_date) query = query.gte('payment_date', start_date);
        if (end_date) query = query.lte('payment_date', end_date);
        
        const { data: payments, error } = await query;
        
        if (error) throw new ValidationError('Failed to fetch payments data');
        
        const headers = include_fields || [
          'id', 'amount', 'currency', 'original_amount', 'original_currency', 
          'exchange_rate', 'payment_date', 'payment_method', 'description', 
          'category', 'vendor', 'reference_number', 'receipt_url', 'notes', 
          'tags', 'status', 'subscription_name', 'created_at'
        ];
        
        const formattedPayments = payments.map(payment => ({
          id: payment.id,
          amount: payment.amount,
          currency: payment.currency,
          original_amount: payment.original_amount || '',
          original_currency: payment.original_currency || '',
          exchange_rate: payment.exchange_rate || '',
          payment_date: payment.payment_date,
          payment_method: payment.payment_method,
          description: payment.description,
          category: payment.category || '',
          vendor: payment.vendor || '',
          reference_number: payment.reference_number || '',
          receipt_url: payment.receipt_url || '',
          notes: payment.notes || '',
          tags: Array.isArray(payment.tags) ? payment.tags.join(';') : '',
          status: payment.status,
          subscription_name: payment.subscriptions?.name || '',
          created_at: payment.created_at
        }));
        
        csvData = convertToCSV(formattedPayments, headers);
        break;
      }
      
      case 'subscriptions': {
        let query = req.supabase
          .from('subscriptions')
          .select('*')
          .eq('business_id', business_id)
          .order('created_at', { ascending: false });
        
        if (start_date) query = query.gte('created_at', start_date);
        if (end_date) query = query.lte('created_at', end_date);
        
        const { data: subscriptions, error } = await query;
        
        if (error) throw new ValidationError('Failed to fetch subscriptions data');
        
        const headers = include_fields || [
          'id', 'name', 'amount', 'currency', 'billing_cycle', 'next_billing_date',
          'description', 'category', 'vendor', 'website', 'notes', 'tags', 
          'status', 'created_at', 'updated_at'
        ];
        
        const formattedSubscriptions = subscriptions.map(sub => ({
          id: sub.id,
          name: sub.name,
          amount: sub.amount,
          currency: sub.currency,
          billing_cycle: sub.billing_cycle,
          next_billing_date: sub.next_billing_date || '',
          description: sub.description || '',
          category: sub.category || '',
          vendor: sub.vendor || '',
          website: sub.website || '',
          notes: sub.notes || '',
          tags: Array.isArray(sub.tags) ? sub.tags.join(';') : '',
          status: sub.status,
          created_at: sub.created_at,
          updated_at: sub.updated_at
        }));
        
        csvData = convertToCSV(formattedSubscriptions, headers);
        break;
      }
      
      case 'reimbursements': {
        let query = req.supabase
          .from('reimbursements')
          .select(`
            id,
            employee_name,
            amount,
            currency,
            original_amount,
            original_currency,
            exchange_rate,
            expense_date,
            reimbursement_date,
            category,
            description,
            receipt_url,
            notes,
            tags,
            status,
            created_at,
            payments(description, amount as payment_amount)
          `)
          .eq('business_id', business_id)
          .order('expense_date', { ascending: false });
        
        if (start_date) query = query.gte('expense_date', start_date);
        if (end_date) query = query.lte('expense_date', end_date);
        
        const { data: reimbursements, error } = await query;
        
        if (error) throw new ValidationError('Failed to fetch reimbursements data');
        
        const headers = include_fields || [
          'id', 'employee_name', 'amount', 'currency', 'original_amount', 
          'original_currency', 'exchange_rate', 'expense_date', 'reimbursement_date',
          'category', 'description', 'receipt_url', 'notes', 'tags', 'status',
          'payment_description', 'payment_amount', 'created_at'
        ];
        
        const formattedReimbursements = reimbursements.map(reimb => ({
          id: reimb.id,
          employee_name: reimb.employee_name,
          amount: reimb.amount,
          currency: reimb.currency,
          original_amount: reimb.original_amount || '',
          original_currency: reimb.original_currency || '',
          exchange_rate: reimb.exchange_rate || '',
          expense_date: reimb.expense_date,
          reimbursement_date: reimb.reimbursement_date || '',
          category: reimb.category,
          description: reimb.description,
          receipt_url: reimb.receipt_url || '',
          notes: reimb.notes || '',
          tags: Array.isArray(reimb.tags) ? reimb.tags.join(';') : '',
          status: reimb.status,
          payment_description: reimb.payments?.description || '',
          payment_amount: reimb.payments?.payment_amount || '',
          created_at: reimb.created_at
        }));
        
        csvData = convertToCSV(formattedReimbursements, headers);
        break;
      }
      
      case 'all': {
        // Export all data types in separate sections
        csvData = 'PAYMENTS\n';
        
        // Add payments
        const { data: payments } = await req.supabase
          .from('payments')
          .select('*')
          .eq('business_id', business_id)
          .order('payment_date', { ascending: false });
        
        if (payments && payments.length > 0) {
          const paymentHeaders = ['id', 'amount', 'currency', 'payment_date', 'payment_method', 'description', 'category', 'status'];
          csvData += convertToCSV(payments, paymentHeaders);
        }
        
        csvData += '\n\nSUBSCRIPTIONS\n';
        
        // Add subscriptions
        const { data: subscriptions } = await req.supabase
          .from('subscriptions')
          .select('*')
          .eq('business_id', business_id)
          .order('created_at', { ascending: false });
        
        if (subscriptions && subscriptions.length > 0) {
          const subscriptionHeaders = ['id', 'name', 'amount', 'currency', 'billing_cycle', 'status', 'created_at'];
          csvData += convertToCSV(subscriptions, subscriptionHeaders);
        }
        
        csvData += '\n\nREIMBURSEMENTS\n';
        
        // Add reimbursements
        const { data: reimbursements } = await req.supabase
          .from('reimbursements')
          .select('*')
          .eq('business_id', business_id)
          .order('expense_date', { ascending: false });
        
        if (reimbursements && reimbursements.length > 0) {
          const reimbursementHeaders = ['id', 'employee_name', 'amount', 'currency', 'expense_date', 'category', 'description', 'status'];
          csvData += convertToCSV(reimbursements, reimbursementHeaders);
        }
        
        filename = `${req.business.name.replace(/[^a-zA-Z0-9]/g, '_')}_complete_export_${new Date().toISOString().split('T')[0]}.csv`;
        break;
      }
      
      default:
        throw new ValidationError('Invalid export type');
    }
    
    logger.info('CSV export generated', {
      userId: req.user.id,
      businessId: business_id,
      exportType: export_type,
      filename,
      dataSize: csvData.length
    });
    
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.send(csvData);
    
  } catch (error) {
    logger.error('Failed to generate CSV export', {
      userId: req.user.id,
      businessId: business_id,
      exportType: export_type,
      error: error.message
    });
    throw new ValidationError('Failed to generate CSV export');
  }
}));

/**
 * @route GET /api/reports/analytics/:business_id
 * @desc Get business analytics data
 * @access Private
 */
router.get('/analytics/:business_id', asyncHandler(async (req, res) => {
  const { business_id } = req.params;
  const { period = '30d' } = req.query;
  
  // Verify business ownership
  const { data: business, error: businessError } = await req.supabase
    .from('businesses')
    .select('id, owner_id, name')
    .eq('id', business_id)
    .single();

  if (businessError || !business) {
    throw new NotFoundError('Business not found');
  }

  if (business.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this business');
  }
  
  // Calculate date range
  const now = new Date();
  let startDate;
  
  switch (period) {
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case '1y':
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }
  
  const startDateStr = startDate.toISOString().split('T')[0];
  const endDateStr = now.toISOString().split('T')[0];
  
  // Get all data in parallel
  const [paymentsResult, subscriptionsResult, reimbursementsResult] = await Promise.all([
    req.supabase
      .from('payments')
      .select('amount, currency, payment_date, status, category')
      .eq('business_id', business_id)
      .gte('payment_date', startDateStr),
    
    req.supabase
      .from('subscriptions')
      .select('amount, currency, billing_cycle, status, created_at')
      .eq('business_id', business_id),
    
    req.supabase
      .from('reimbursements')
      .select('amount, currency, expense_date, status, category')
      .eq('business_id', business_id)
      .gte('expense_date', startDateStr)
  ]);
  
  const payments = paymentsResult.data || [];
  const subscriptions = subscriptionsResult.data || [];
  const reimbursements = reimbursementsResult.data || [];
  
  // Calculate trends (compare with previous period)
  const previousStartDate = new Date(startDate.getTime() - (now.getTime() - startDate.getTime()));
  const previousStartDateStr = previousStartDate.toISOString().split('T')[0];
  
  const [previousPaymentsResult, previousReimbursementsResult] = await Promise.all([
    req.supabase
      .from('payments')
      .select('amount')
      .eq('business_id', business_id)
      .gte('payment_date', previousStartDateStr)
      .lt('payment_date', startDateStr),
    
    req.supabase
      .from('reimbursements')
      .select('amount')
      .eq('business_id', business_id)
      .gte('expense_date', previousStartDateStr)
      .lt('expense_date', startDateStr)
  ]);
  
  const previousPayments = previousPaymentsResult.data || [];
  const previousReimbursements = previousReimbursementsResult.data || [];
  
  // Calculate analytics
  const currentPaymentsTotal = payments.reduce((sum, p) => sum + parseFloat(p.amount || 0), 0);
  const previousPaymentsTotal = previousPayments.reduce((sum, p) => sum + parseFloat(p.amount || 0), 0);
  const paymentsGrowth = previousPaymentsTotal > 0 ? 
    ((currentPaymentsTotal - previousPaymentsTotal) / previousPaymentsTotal) * 100 : 0;
  
  const currentReimbursementsTotal = reimbursements.reduce((sum, r) => sum + parseFloat(r.amount || 0), 0);
  const previousReimbursementsTotal = previousReimbursements.reduce((sum, r) => sum + parseFloat(r.amount || 0), 0);
  const reimbursementsGrowth = previousReimbursementsTotal > 0 ? 
    ((currentReimbursementsTotal - previousReimbursementsTotal) / previousReimbursementsTotal) * 100 : 0;
  
  const analytics = {
    period: {
      startDate: startDateStr,
      endDate: endDateStr,
      days: Math.ceil((now - startDate) / (1000 * 60 * 60 * 24))
    },
    overview: {
      totalPayments: currentPaymentsTotal,
      totalReimbursements: currentReimbursementsTotal,
      totalSubscriptions: subscriptions.reduce((sum, s) => sum + parseFloat(s.amount || 0), 0),
      paymentsGrowth: Math.round(paymentsGrowth * 100) / 100,
      reimbursementsGrowth: Math.round(reimbursementsGrowth * 100) / 100,
      activeSubscriptions: subscriptions.filter(s => s.status === 'active').length
    },
    trends: {
      dailyPayments: {},
      dailyReimbursements: {},
      monthlyRecurring: {}
    },
    insights: {
      topCategories: {},
      paymentMethods: {},
      subscriptionCycles: {}
    }
  };
  
  // Calculate daily trends
  payments.forEach(payment => {
    const date = payment.payment_date;
    if (!analytics.trends.dailyPayments[date]) {
      analytics.trends.dailyPayments[date] = 0;
    }
    analytics.trends.dailyPayments[date] += parseFloat(payment.amount || 0);
  });
  
  reimbursements.forEach(reimbursement => {
    const date = reimbursement.expense_date;
    if (!analytics.trends.dailyReimbursements[date]) {
      analytics.trends.dailyReimbursements[date] = 0;
    }
    analytics.trends.dailyReimbursements[date] += parseFloat(reimbursement.amount || 0);
  });
  
  // Calculate insights
  payments.forEach(payment => {
    const category = payment.category || 'Uncategorized';
    if (!analytics.insights.topCategories[category]) {
      analytics.insights.topCategories[category] = 0;
    }
    analytics.insights.topCategories[category] += parseFloat(payment.amount || 0);
  });
  
  subscriptions.forEach(subscription => {
    const cycle = subscription.billing_cycle || 'Unknown';
    if (!analytics.insights.subscriptionCycles[cycle]) {
      analytics.insights.subscriptionCycles[cycle] = { count: 0, amount: 0 };
    }
    analytics.insights.subscriptionCycles[cycle].count++;
    analytics.insights.subscriptionCycles[cycle].amount += parseFloat(subscription.amount || 0);
  });
  
  logger.info('Analytics report generated', {
    userId: req.user.id,
    businessId: business_id,
    period,
    paymentsCount: payments.length,
    reimbursementsCount: reimbursements.length,
    subscriptionsCount: subscriptions.length
  });
  
  res.json({ analytics });
}));

module.exports = router;