{"name": "subscription-dashboard-backend", "version": "1.0.0", "description": "Backend API for Subscription Dashboard with Supabase integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "lint": "eslint src/", "migrate": "node src/scripts/migrate.js", "seed": "node src/scripts/seed.js", "cron:exchange-rates": "node src/scripts/updateExchangeRates.js"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "zod": "^3.22.4", "node-cron": "^3.0.3", "axios": "^1.6.2", "nodemailer": "^6.9.7", "winston": "^3.11.0", "compression": "^1.7.4", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "csv-writer": "^1.6.0", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0"}, "keywords": ["subscription", "dashboard", "supabase", "express", "api"], "author": "Your Name", "license": "MIT"}