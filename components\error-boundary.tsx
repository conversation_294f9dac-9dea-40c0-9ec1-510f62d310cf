"use client"

import React from "react"
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Error caught by boundary:", error, errorInfo)
    
    // Enhanced error logging with context
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : 'unknown',
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'unknown',
    }
    
    // Log to console with detailed info
    console.group('🚨 Error Boundary Report')
    console.error('Error:', error)
    console.error('Component Stack:', errorInfo.componentStack)
    console.error('Full Report:', errorReport)
    console.groupEnd()
    
    // In production, you would send this to an error reporting service
    // Example: Sentry, LogRocket, Bugsnag, etc.
    if (process.env.NODE_ENV === 'production') {
      // TODO: Implement error reporting service
      // reportErrorToService(errorReport)
    }
    
    this.setState({
      error,
      errorInfo,
    })
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />
      }

      return <DefaultErrorFallback error={this.state.error} resetError={this.resetError} />
    }

    return this.props.children
  }
}

interface ErrorFallbackProps {
  error?: Error
  resetError: () => void
}

function DefaultErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30">
            <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-xl">Something went wrong</CardTitle>
          <CardDescription>
            An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <details className="rounded-md bg-gray-50 p-3 dark:bg-gray-900/50">
              <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300">
                Error Details
              </summary>
              <pre className="mt-2 text-xs text-gray-600 dark:text-gray-400 overflow-auto">
                {error.message}
                {error.stack && (
                  <>
                    {"\n\n"}
                    {error.stack}
                  </>
                )}
              </pre>
            </details>
          )}
          <div className="flex gap-2">
            <Button onClick={resetError} className="flex-1">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="flex-1"
            >
              Reload Page
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Hook for functional components to handle errors
export function useErrorHandler() {
  return (error: Error, errorInfo?: { componentStack?: string }) => {
    console.error("Error caught by hook:", error, errorInfo)
    
    // Enhanced error logging
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : 'unknown',
    }
    
    console.group('🚨 useErrorHandler Report')
    console.error('Error:', error)
    console.error('Context:', errorInfo)
    console.error('Full Report:', errorReport)
    console.groupEnd()
    
    // In production, send to error reporting service
    if (process.env.NODE_ENV === 'production') {
      // TODO: Implement error reporting service
      // reportErrorToService(errorReport)
    }
  }
}

// Hook for handling async operations with retry logic
export function useAsyncErrorHandler() {
  const handleError = useErrorHandler()
  
  const executeWithRetry = async <T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> => {
    let lastError: Error
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error as Error
        
        if (attempt === maxRetries) {
          handleError(lastError, { componentStack: `Async operation failed after ${maxRetries} attempts` })
          throw lastError
        }
        
        // Exponential backoff
        const retryDelay = delay * Math.pow(2, attempt - 1)
        console.warn(`Attempt ${attempt} failed, retrying in ${retryDelay}ms...`, error)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      }
    }
    
    throw lastError!
  }
  
  return { executeWithRetry, handleError }
}

// Higher-order component for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Specific error fallback for form components
export function FormErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
      <CardHeader>
        <CardTitle className="text-red-800 dark:text-red-200 flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5" />
          Form Error
        </CardTitle>
        <CardDescription className="text-red-700 dark:text-red-300">
          There was an error with the form. Please try again.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Button onClick={resetError} variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Reset Form
        </Button>
      </CardContent>
    </Card>
  )
}

// Specific error fallback for data loading components
export function DataErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <Card className="border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20">
      <CardHeader>
        <CardTitle className="text-yellow-800 dark:text-yellow-200 flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5" />
          Data Loading Error
        </CardTitle>
        <CardDescription className="text-yellow-700 dark:text-yellow-300">
          Failed to load data. Please check your connection and try again.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Button onClick={resetError} variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Retry
        </Button>
      </CardContent>
    </Card>
  )
}

export default ErrorBoundary
