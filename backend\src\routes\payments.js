const express = require('express');
const { z } = require('zod');
const { as<PERSON><PERSON><PERSON><PERSON>, ValidationError, NotFoundError, ForbiddenError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const createPaymentSchema = z.object({
  business_id: z.string().uuid('Invalid business ID'),
  subscription_id: z.string().uuid('Invalid subscription ID').optional(),
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().length(3, 'Currency must be 3 characters').default('KWD'),
  original_amount: z.number().positive('Original amount must be positive').optional(),
  original_currency: z.string().length(3, 'Original currency must be 3 characters').optional(),
  exchange_rate: z.number().positive('Exchange rate must be positive').optional(),
  payment_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Payment date must be in YYYY-MM-DD format'),
  payment_method: z.string().min(1, 'Payment method is required'),
  description: z.string().min(1, 'Description is required'),
  category: z.string().optional(),
  vendor: z.string().optional(),
  reference_number: z.string().optional(),
  receipt_url: z.string().url().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional().default([])
});

const updatePaymentSchema = z.object({
  amount: z.number().positive('Amount must be positive').optional(),
  currency: z.string().length(3, 'Currency must be 3 characters').optional(),
  original_amount: z.number().positive('Original amount must be positive').optional(),
  original_currency: z.string().length(3, 'Original currency must be 3 characters').optional(),
  exchange_rate: z.number().positive('Exchange rate must be positive').optional(),
  payment_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Payment date must be in YYYY-MM-DD format').optional(),
  payment_method: z.string().min(1, 'Payment method is required').optional(),
  description: z.string().min(1, 'Description is required').optional(),
  category: z.string().optional(),
  vendor: z.string().optional(),
  reference_number: z.string().optional(),
  receipt_url: z.string().url().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['pending', 'completed', 'failed', 'cancelled']).optional()
});

/**
 * Middleware to verify business ownership
 */
const verifyBusinessOwnership = asyncHandler(async (req, res, next) => {
  const businessId = req.body.business_id || req.query.business_id;
  
  if (!businessId) {
    throw new ValidationError('Business ID is required');
  }

  const { data: business, error } = await req.supabase
    .from('businesses')
    .select('id, owner_id, name')
    .eq('id', businessId)
    .single();

  if (error || !business) {
    throw new NotFoundError('Business not found');
  }

  if (business.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this business');
  }

  req.business = business;
  next();
});

/**
 * Middleware to verify payment ownership
 */
const verifyPaymentOwnership = asyncHandler(async (req, res, next) => {
  const paymentId = req.params.id || req.params.paymentId;
  
  if (!paymentId) {
    throw new ValidationError('Payment ID is required');
  }

  const { data: payment, error } = await req.supabase
    .from('payments')
    .select(`
      id, 
      business_id, 
      subscription_id,
      amount,
      currency,
      description,
      businesses!inner(id, owner_id, name)
    `)
    .eq('id', paymentId)
    .single();

  if (error || !payment) {
    throw new NotFoundError('Payment not found');
  }

  if (payment.businesses.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this payment');
  }

  req.payment = payment;
  next();
});

/**
 * @route POST /api/payments
 * @desc Create a new payment
 * @access Private
 */
router.post('/', verifyBusinessOwnership, asyncHandler(async (req, res) => {
  const validatedData = createPaymentSchema.parse(req.body);
  
  // Verify subscription belongs to the business if provided
  if (validatedData.subscription_id) {
    const { data: subscription, error: subError } = await req.supabase
      .from('subscriptions')
      .select('id, business_id')
      .eq('id', validatedData.subscription_id)
      .eq('business_id', validatedData.business_id)
      .single();

    if (subError || !subscription) {
      throw new ValidationError('Subscription not found or does not belong to this business');
    }
  }

  // Calculate exchange rate and converted amount if needed
  let finalAmount = validatedData.amount;
  let exchangeRate = validatedData.exchange_rate;
  
  if (validatedData.original_amount && validatedData.original_currency && 
      validatedData.original_currency !== validatedData.currency) {
    
    if (!exchangeRate) {
      // Fetch current exchange rate
      const { data: rateData } = await req.supabase
        .from('exchange_rates')
        .select('rate')
        .eq('base_currency', 'KWD')
        .eq('target_currency', validatedData.original_currency)
        .eq('date', new Date().toISOString().split('T')[0])
        .single();
      
      if (rateData) {
        exchangeRate = rateData.rate;
        finalAmount = validatedData.original_amount / exchangeRate;
      }
    } else {
      finalAmount = validatedData.original_amount / exchangeRate;
    }
  }

  const paymentData = {
    business_id: validatedData.business_id,
    subscription_id: validatedData.subscription_id,
    amount: finalAmount,
    currency: validatedData.currency,
    original_amount: validatedData.original_amount,
    original_currency: validatedData.original_currency,
    exchange_rate: exchangeRate,
    payment_date: validatedData.payment_date,
    payment_method: validatedData.payment_method,
    description: validatedData.description,
    category: validatedData.category,
    vendor: validatedData.vendor,
    reference_number: validatedData.reference_number,
    receipt_url: validatedData.receipt_url,
    notes: validatedData.notes,
    tags: validatedData.tags,
    status: 'completed'
  };

  const { data: payment, error } = await req.supabase
    .from('payments')
    .insert(paymentData)
    .select()
    .single();

  if (error) {
    logger.error('Failed to create payment', { 
      userId: req.user.id, 
      businessId: validatedData.business_id,
      error: error.message,
      paymentData: { 
        amount: validatedData.amount, 
        description: validatedData.description 
      }
    });
    throw new ValidationError('Failed to create payment');
  }

  logger.logPayment('created', payment.id, payment.business_id, req.user.id, 
    payment.amount, payment.currency, {
      description: payment.description,
      paymentMethod: payment.payment_method
    }
  );

  res.status(201).json({
    message: 'Payment created successfully',
    payment: {
      id: payment.id,
      businessId: payment.business_id,
      subscriptionId: payment.subscription_id,
      amount: payment.amount,
      currency: payment.currency,
      originalAmount: payment.original_amount,
      originalCurrency: payment.original_currency,
      exchangeRate: payment.exchange_rate,
      paymentDate: payment.payment_date,
      paymentMethod: payment.payment_method,
      description: payment.description,
      category: payment.category,
      vendor: payment.vendor,
      referenceNumber: payment.reference_number,
      receiptUrl: payment.receipt_url,
      notes: payment.notes,
      tags: payment.tags,
      status: payment.status,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    }
  });
}));

/**
 * @route GET /api/payments
 * @desc Get all payments for a business
 * @access Private
 */
router.get('/', asyncHandler(async (req, res) => {
  const { 
    business_id, 
    page = 1, 
    limit = 10, 
    search, 
    status, 
    category, 
    payment_method,
    start_date,
    end_date,
    subscription_id,
    sort_by = 'payment_date',
    sort_order = 'desc'
  } = req.query;

  if (!business_id) {
    throw new ValidationError('Business ID is required');
  }

  // Verify business ownership
  const { data: business, error: businessError } = await req.supabase
    .from('businesses')
    .select('id, owner_id')
    .eq('id', business_id)
    .single();

  if (businessError || !business) {
    throw new NotFoundError('Business not found');
  }

  if (business.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this business');
  }

  const offset = (page - 1) * limit;
  const validSortColumns = ['amount', 'payment_date', 'created_at', 'status', 'description'];
  const sortColumn = validSortColumns.includes(sort_by) ? sort_by : 'payment_date';
  const sortDirection = sort_order === 'asc' ? true : false;

  let query = req.supabase
    .from('payments')
    .select(`
      *,
      subscriptions(id, name)
    `, { count: 'exact' })
    .eq('business_id', business_id)
    .order(sortColumn, { ascending: sortDirection })
    .range(offset, offset + limit - 1);

  // Apply filters
  if (search) {
    query = query.or(`description.ilike.%${search}%,vendor.ilike.%${search}%,reference_number.ilike.%${search}%`);
  }

  if (status) {
    query = query.eq('status', status);
  }

  if (category) {
    query = query.eq('category', category);
  }

  if (payment_method) {
    query = query.eq('payment_method', payment_method);
  }

  if (subscription_id) {
    query = query.eq('subscription_id', subscription_id);
  }

  if (start_date) {
    query = query.gte('payment_date', start_date);
  }

  if (end_date) {
    query = query.lte('payment_date', end_date);
  }

  const { data: payments, error, count } = await query;

  if (error) {
    logger.error('Failed to fetch payments', { 
      userId: req.user.id, 
      businessId: business_id,
      error: error.message 
    });
    throw new ValidationError('Failed to fetch payments');
  }

  const totalPages = Math.ceil(count / limit);

  res.json({
    payments: payments.map(payment => ({
      id: payment.id,
      businessId: payment.business_id,
      subscriptionId: payment.subscription_id,
      subscriptionName: payment.subscriptions?.name,
      amount: payment.amount,
      currency: payment.currency,
      originalAmount: payment.original_amount,
      originalCurrency: payment.original_currency,
      exchangeRate: payment.exchange_rate,
      paymentDate: payment.payment_date,
      paymentMethod: payment.payment_method,
      description: payment.description,
      category: payment.category,
      vendor: payment.vendor,
      referenceNumber: payment.reference_number,
      receiptUrl: payment.receipt_url,
      notes: payment.notes,
      tags: payment.tags,
      status: payment.status,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    })),
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: count,
      itemsPerPage: parseInt(limit),
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    }
  });
}));

/**
 * @route GET /api/payments/:id
 * @desc Get a specific payment
 * @access Private
 */
router.get('/:id', verifyPaymentOwnership, asyncHandler(async (req, res) => {
  const { data: payment, error } = await req.supabase
    .from('payments')
    .select(`
      *,
      subscriptions(id, name),
      businesses(id, name)
    `)
    .eq('id', req.params.id)
    .single();

  if (error) {
    logger.error('Failed to fetch payment', { 
      userId: req.user.id, 
      paymentId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to fetch payment');
  }

  res.json({
    payment: {
      id: payment.id,
      businessId: payment.business_id,
      businessName: payment.businesses?.name,
      subscriptionId: payment.subscription_id,
      subscriptionName: payment.subscriptions?.name,
      amount: payment.amount,
      currency: payment.currency,
      originalAmount: payment.original_amount,
      originalCurrency: payment.original_currency,
      exchangeRate: payment.exchange_rate,
      paymentDate: payment.payment_date,
      paymentMethod: payment.payment_method,
      description: payment.description,
      category: payment.category,
      vendor: payment.vendor,
      referenceNumber: payment.reference_number,
      receiptUrl: payment.receipt_url,
      notes: payment.notes,
      tags: payment.tags,
      status: payment.status,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    }
  });
}));

/**
 * @route PUT /api/payments/:id
 * @desc Update a payment
 * @access Private
 */
router.put('/:id', verifyPaymentOwnership, asyncHandler(async (req, res) => {
  const validatedData = updatePaymentSchema.parse(req.body);
  
  // Remove undefined values
  const updateData = Object.fromEntries(
    Object.entries(validatedData).filter(([_, value]) => value !== undefined)
  );

  if (Object.keys(updateData).length === 0) {
    throw new ValidationError('No valid fields to update');
  }

  // Recalculate amount if exchange rate or original amount changed
  if ((updateData.original_amount || updateData.exchange_rate) && 
      req.payment.original_currency && req.payment.original_currency !== req.payment.currency) {
    
    const originalAmount = updateData.original_amount || req.payment.original_amount;
    const exchangeRate = updateData.exchange_rate || req.payment.exchange_rate;
    
    if (originalAmount && exchangeRate) {
      updateData.amount = originalAmount / exchangeRate;
    }
  }

  const { data: payment, error } = await req.supabase
    .from('payments')
    .update(updateData)
    .eq('id', req.params.id)
    .select()
    .single();

  if (error) {
    logger.error('Failed to update payment', { 
      userId: req.user.id, 
      paymentId: req.params.id,
      error: error.message,
      updateData
    });
    throw new ValidationError('Failed to update payment');
  }

  logger.logPayment('updated', payment.id, payment.business_id, req.user.id, 
    payment.amount, payment.currency, {
      description: payment.description,
      updatedFields: Object.keys(updateData)
    }
  );

  res.json({
    message: 'Payment updated successfully',
    payment: {
      id: payment.id,
      businessId: payment.business_id,
      subscriptionId: payment.subscription_id,
      amount: payment.amount,
      currency: payment.currency,
      originalAmount: payment.original_amount,
      originalCurrency: payment.original_currency,
      exchangeRate: payment.exchange_rate,
      paymentDate: payment.payment_date,
      paymentMethod: payment.payment_method,
      description: payment.description,
      category: payment.category,
      vendor: payment.vendor,
      referenceNumber: payment.reference_number,
      receiptUrl: payment.receipt_url,
      notes: payment.notes,
      tags: payment.tags,
      status: payment.status,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    }
  });
}));

/**
 * @route DELETE /api/payments/:id
 * @desc Delete a payment
 * @access Private
 */
router.delete('/:id', verifyPaymentOwnership, asyncHandler(async (req, res) => {
  const { error } = await req.supabase
    .from('payments')
    .delete()
    .eq('id', req.params.id);

  if (error) {
    logger.error('Failed to delete payment', { 
      userId: req.user.id, 
      paymentId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to delete payment');
  }

  logger.logPayment('deleted', req.params.id, req.payment.business_id, req.user.id, 
    req.payment.amount, req.payment.currency, {
      description: req.payment.description
    }
  );

  res.json({
    message: 'Payment deleted successfully'
  });
}));

/**
 * @route GET /api/payments/summary/:business_id
 * @desc Get payment summary for a business
 * @access Private
 */
router.get('/summary/:business_id', asyncHandler(async (req, res) => {
  const { business_id } = req.params;
  const { period = '30d' } = req.query;
  
  // Verify business ownership
  const { data: business, error: businessError } = await req.supabase
    .from('businesses')
    .select('id, owner_id')
    .eq('id', business_id)
    .single();

  if (businessError || !business) {
    throw new NotFoundError('Business not found');
  }

  if (business.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this business');
  }

  // Calculate date range
  const now = new Date();
  let startDate;
  
  switch (period) {
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case '1y':
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  const { data: payments, error } = await req.supabase
    .from('payments')
    .select('amount, currency, payment_date, status, category, payment_method')
    .eq('business_id', business_id)
    .gte('payment_date', startDate.toISOString().split('T')[0]);

  if (error) {
    logger.error('Failed to fetch payment summary', { 
      userId: req.user.id, 
      businessId: business_id,
      error: error.message 
    });
    throw new ValidationError('Failed to fetch payment summary');
  }

  // Calculate summary statistics
  const summary = {
    totalPayments: payments?.length || 0,
    totalAmount: payments?.reduce((sum, p) => sum + parseFloat(p.amount || 0), 0) || 0,
    completedPayments: payments?.filter(p => p.status === 'completed').length || 0,
    pendingPayments: payments?.filter(p => p.status === 'pending').length || 0,
    failedPayments: payments?.filter(p => p.status === 'failed').length || 0,
    byCategory: {},
    byPaymentMethod: {},
    byMonth: {},
    period: {
      startDate: startDate.toISOString().split('T')[0],
      endDate: now.toISOString().split('T')[0],
      days: Math.ceil((now - startDate) / (1000 * 60 * 60 * 24))
    }
  };

  // Group by category
  payments?.forEach(payment => {
    const category = payment.category || 'Uncategorized';
    if (!summary.byCategory[category]) {
      summary.byCategory[category] = { count: 0, amount: 0 };
    }
    summary.byCategory[category].count++;
    summary.byCategory[category].amount += parseFloat(payment.amount || 0);
  });

  // Group by payment method
  payments?.forEach(payment => {
    const method = payment.payment_method || 'Unknown';
    if (!summary.byPaymentMethod[method]) {
      summary.byPaymentMethod[method] = { count: 0, amount: 0 };
    }
    summary.byPaymentMethod[method].count++;
    summary.byPaymentMethod[method].amount += parseFloat(payment.amount || 0);
  });

  // Group by month
  payments?.forEach(payment => {
    const month = payment.payment_date.substring(0, 7); // YYYY-MM
    if (!summary.byMonth[month]) {
      summary.byMonth[month] = { count: 0, amount: 0 };
    }
    summary.byMonth[month].count++;
    summary.byMonth[month].amount += parseFloat(payment.amount || 0);
  });

  res.json({ summary });
}));

module.exports = router;