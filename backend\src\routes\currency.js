const express = require('express');
const { z } = require('zod');
const { async<PERSON><PERSON><PERSON>, ValidationError, NotFoundError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { optionalAuthMiddleware } = require('../middleware/auth');

const router = express.Router();

// Validation schemas
const convertCurrencySchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  from_currency: z.string().length(3, 'From currency must be 3 characters'),
  to_currency: z.string().length(3, 'To currency must be 3 characters'),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format').optional()
});

const historicalRatesSchema = z.object({
  base_currency: z.string().length(3, 'Base currency must be 3 characters').default('KWD'),
  target_currency: z.string().length(3, 'Target currency must be 3 characters'),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date must be in YYYY-MM-DD format'),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date must be in YYYY-MM-DD format')
});

/**
 * @route GET /api/currency/rates
 * @desc Get current exchange rates
 * @access Public
 */
router.get('/rates', optionalAuthMiddleware, asyncHandler(async (req, res) => {
  const { base_currency = 'KWD', target_currencies } = req.query;
  
  const today = new Date().toISOString().split('T')[0];
  
  let query = req.supabase
    .from('exchange_rates')
    .select('*')
    .eq('base_currency', base_currency)
    .eq('date', today)
    .order('target_currency');
  
  // Filter by specific target currencies if provided
  if (target_currencies) {
    const currencies = target_currencies.split(',').map(c => c.trim().toUpperCase());
    query = query.in('target_currency', currencies);
  }
  
  const { data: rates, error } = await query;
  
  if (error) {
    logger.error('Failed to fetch exchange rates', { 
      userId: req.user?.id,
      baseCurrency: base_currency,
      error: error.message 
    });
    throw new ValidationError('Failed to fetch exchange rates');
  }
  
  // If no rates found for today, try to get the most recent rates
  if (!rates || rates.length === 0) {
    const { data: recentRates, error: recentError } = await req.supabase
      .from('exchange_rates')
      .select('*')
      .eq('base_currency', base_currency)
      .order('date', { ascending: false })
      .limit(50); // Get recent rates for multiple currencies
    
    if (recentError) {
      logger.error('Failed to fetch recent exchange rates', { 
        userId: req.user?.id,
        baseCurrency: base_currency,
        error: recentError.message 
      });
      throw new ValidationError('Failed to fetch exchange rates');
    }
    
    // Group by currency and get the most recent rate for each
    const latestRates = {};
    recentRates?.forEach(rate => {
      if (!latestRates[rate.target_currency] || 
          new Date(rate.date) > new Date(latestRates[rate.target_currency].date)) {
        latestRates[rate.target_currency] = rate;
      }
    });
    
    const formattedRates = Object.values(latestRates).map(rate => ({
      baseCurrency: rate.base_currency,
      targetCurrency: rate.target_currency,
      rate: parseFloat(rate.rate),
      date: rate.date,
      source: rate.source,
      lastUpdated: rate.updated_at
    }));
    
    return res.json({
      rates: formattedRates,
      baseCurrency: base_currency,
      date: formattedRates[0]?.date || today,
      isHistorical: true,
      message: 'Current rates not available, showing most recent rates'
    });
  }
  
  const formattedRates = rates.map(rate => ({
    baseCurrency: rate.base_currency,
    targetCurrency: rate.target_currency,
    rate: parseFloat(rate.rate),
    date: rate.date,
    source: rate.source,
    lastUpdated: rate.updated_at
  }));
  
  logger.logCurrency('rates_fetched', req.user?.id, {
    baseCurrency: base_currency,
    ratesCount: formattedRates.length,
    targetCurrencies: formattedRates.map(r => r.targetCurrency)
  });
  
  res.json({
    rates: formattedRates,
    baseCurrency: base_currency,
    date: today,
    isHistorical: false
  });
}));

/**
 * @route GET /api/currency/rates/:base/:target
 * @desc Get specific exchange rate between two currencies
 * @access Public
 */
router.get('/rates/:base/:target', optionalAuthMiddleware, asyncHandler(async (req, res) => {
  const { base, target } = req.params;
  const { date } = req.query;
  
  const baseCurrency = base.toUpperCase();
  const targetCurrency = target.toUpperCase();
  const queryDate = date || new Date().toISOString().split('T')[0];
  
  // Validate currency codes
  if (baseCurrency.length !== 3 || targetCurrency.length !== 3) {
    throw new ValidationError('Currency codes must be 3 characters long');
  }
  
  // If same currency, return rate of 1
  if (baseCurrency === targetCurrency) {
    return res.json({
      baseCurrency,
      targetCurrency,
      rate: 1,
      date: queryDate,
      source: 'system',
      isHistorical: date ? true : false
    });
  }
  
  const { data: rate, error } = await req.supabase
    .from('exchange_rates')
    .select('*')
    .eq('base_currency', baseCurrency)
    .eq('target_currency', targetCurrency)
    .eq('date', queryDate)
    .single();
  
  if (error || !rate) {
    // Try to find the most recent rate if specific date not found
    const { data: recentRate, error: recentError } = await req.supabase
      .from('exchange_rates')
      .select('*')
      .eq('base_currency', baseCurrency)
      .eq('target_currency', targetCurrency)
      .order('date', { ascending: false })
      .limit(1)
      .single();
    
    if (recentError || !recentRate) {
      // Try reverse rate (target to base) and calculate inverse
      const { data: reverseRate, error: reverseError } = await req.supabase
        .from('exchange_rates')
        .select('*')
        .eq('base_currency', targetCurrency)
        .eq('target_currency', baseCurrency)
        .order('date', { ascending: false })
        .limit(1)
        .single();
      
      if (reverseError || !reverseRate) {
        throw new NotFoundError(`Exchange rate not found for ${baseCurrency} to ${targetCurrency}`);
      }
      
      const inverseRate = 1 / parseFloat(reverseRate.rate);
      
      logger.logCurrency('rate_fetched', req.user?.id, {
        baseCurrency,
        targetCurrency,
        rate: inverseRate,
        date: reverseRate.date,
        isReverse: true
      });
      
      return res.json({
        baseCurrency,
        targetCurrency,
        rate: inverseRate,
        date: reverseRate.date,
        source: reverseRate.source,
        isHistorical: true,
        isReverse: true,
        message: 'Rate calculated from reverse exchange rate'
      });
    }
    
    logger.logCurrency('rate_fetched', req.user?.id, {
      baseCurrency,
      targetCurrency,
      rate: parseFloat(recentRate.rate),
      date: recentRate.date,
      isHistorical: true
    });
    
    return res.json({
      baseCurrency,
      targetCurrency,
      rate: parseFloat(recentRate.rate),
      date: recentRate.date,
      source: recentRate.source,
      isHistorical: true,
      message: `Rate for ${queryDate} not found, showing most recent rate`
    });
  }
  
  logger.logCurrency('rate_fetched', req.user?.id, {
    baseCurrency,
    targetCurrency,
    rate: parseFloat(rate.rate),
    date: rate.date
  });
  
  res.json({
    baseCurrency,
    targetCurrency,
    rate: parseFloat(rate.rate),
    date: rate.date,
    source: rate.source,
    isHistorical: date ? true : false,
    lastUpdated: rate.updated_at
  });
}));

/**
 * @route POST /api/currency/convert
 * @desc Convert amount between currencies
 * @access Public
 */
router.post('/convert', optionalAuthMiddleware, asyncHandler(async (req, res) => {
  const validatedData = convertCurrencySchema.parse(req.body);
  const { amount, from_currency, to_currency, date } = validatedData;
  
  const fromCurrency = from_currency.toUpperCase();
  const toCurrency = to_currency.toUpperCase();
  const queryDate = date || new Date().toISOString().split('T')[0];
  
  // If same currency, return original amount
  if (fromCurrency === toCurrency) {
    return res.json({
      originalAmount: amount,
      convertedAmount: amount,
      fromCurrency,
      toCurrency,
      rate: 1,
      date: queryDate,
      source: 'system'
    });
  }
  
  // Get exchange rate
  let exchangeRate = null;
  let rateSource = null;
  let rateDate = null;
  let isReverse = false;
  
  // Try direct rate (from -> to)
  const { data: directRate } = await req.supabase
    .from('exchange_rates')
    .select('*')
    .eq('base_currency', fromCurrency)
    .eq('target_currency', toCurrency)
    .eq('date', queryDate)
    .single();
  
  if (directRate) {
    exchangeRate = parseFloat(directRate.rate);
    rateSource = directRate.source;
    rateDate = directRate.date;
  } else {
    // Try most recent direct rate
    const { data: recentDirectRate } = await req.supabase
      .from('exchange_rates')
      .select('*')
      .eq('base_currency', fromCurrency)
      .eq('target_currency', toCurrency)
      .order('date', { ascending: false })
      .limit(1)
      .single();
    
    if (recentDirectRate) {
      exchangeRate = parseFloat(recentDirectRate.rate);
      rateSource = recentDirectRate.source;
      rateDate = recentDirectRate.date;
    } else {
      // Try reverse rate (to -> from) and calculate inverse
      const { data: reverseRate } = await req.supabase
        .from('exchange_rates')
        .select('*')
        .eq('base_currency', toCurrency)
        .eq('target_currency', fromCurrency)
        .order('date', { ascending: false })
        .limit(1)
        .single();
      
      if (reverseRate) {
        exchangeRate = 1 / parseFloat(reverseRate.rate);
        rateSource = reverseRate.source;
        rateDate = reverseRate.date;
        isReverse = true;
      } else {
        // Try cross-currency conversion through KWD
        let fromToKwd = null;
        let kwdToTarget = null;
        
        if (fromCurrency !== 'KWD') {
          const { data: fromKwdRate } = await req.supabase
            .from('exchange_rates')
            .select('*')
            .eq('base_currency', 'KWD')
            .eq('target_currency', fromCurrency)
            .order('date', { ascending: false })
            .limit(1)
            .single();
          
          if (fromKwdRate) {
            fromToKwd = 1 / parseFloat(fromKwdRate.rate); // Convert from currency to KWD
          }
        } else {
          fromToKwd = 1; // Already in KWD
        }
        
        if (toCurrency !== 'KWD') {
          const { data: kwdTargetRate } = await req.supabase
            .from('exchange_rates')
            .select('*')
            .eq('base_currency', 'KWD')
            .eq('target_currency', toCurrency)
            .order('date', { ascending: false })
            .limit(1)
            .single();
          
          if (kwdTargetRate) {
            kwdToTarget = parseFloat(kwdTargetRate.rate);
            rateSource = kwdTargetRate.source;
            rateDate = kwdTargetRate.date;
          }
        } else {
          kwdToTarget = 1; // Converting to KWD
        }
        
        if (fromToKwd !== null && kwdToTarget !== null) {
          exchangeRate = fromToKwd * kwdToTarget;
          isReverse = false;
        }
      }
    }
  }
  
  if (exchangeRate === null) {
    throw new NotFoundError(`Exchange rate not available for ${fromCurrency} to ${toCurrency}`);
  }
  
  const convertedAmount = amount * exchangeRate;
  
  logger.logCurrency('conversion', req.user?.id, {
    originalAmount: amount,
    convertedAmount,
    fromCurrency,
    toCurrency,
    rate: exchangeRate,
    isReverse
  });
  
  res.json({
    originalAmount: amount,
    convertedAmount: Math.round(convertedAmount * 100) / 100, // Round to 2 decimal places
    fromCurrency,
    toCurrency,
    rate: exchangeRate,
    date: rateDate || queryDate,
    source: rateSource,
    isReverse,
    isHistorical: rateDate !== queryDate
  });
}));

/**
 * @route GET /api/currency/historical
 * @desc Get historical exchange rates
 * @access Public
 */
router.get('/historical', optionalAuthMiddleware, asyncHandler(async (req, res) => {
  const validatedData = historicalRatesSchema.parse(req.query);
  const { base_currency, target_currency, start_date, end_date } = validatedData;
  
  const baseCurrency = base_currency.toUpperCase();
  const targetCurrency = target_currency.toUpperCase();
  
  // Validate date range
  const startDate = new Date(start_date);
  const endDate = new Date(end_date);
  const today = new Date();
  
  if (startDate > endDate) {
    throw new ValidationError('Start date must be before end date');
  }
  
  if (endDate > today) {
    throw new ValidationError('End date cannot be in the future');
  }
  
  // Limit to 1 year of data
  const oneYearAgo = new Date();
  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
  
  if (startDate < oneYearAgo) {
    throw new ValidationError('Historical data is limited to the past year');
  }
  
  const { data: rates, error } = await req.supabase
    .from('exchange_rates')
    .select('*')
    .eq('base_currency', baseCurrency)
    .eq('target_currency', targetCurrency)
    .gte('date', start_date)
    .lte('date', end_date)
    .order('date', { ascending: true });
  
  if (error) {
    logger.error('Failed to fetch historical exchange rates', { 
      userId: req.user?.id,
      baseCurrency,
      targetCurrency,
      startDate: start_date,
      endDate: end_date,
      error: error.message 
    });
    throw new ValidationError('Failed to fetch historical exchange rates');
  }
  
  const formattedRates = rates?.map(rate => ({
    date: rate.date,
    rate: parseFloat(rate.rate),
    source: rate.source,
    lastUpdated: rate.updated_at
  })) || [];
  
  // Calculate statistics
  const rateValues = formattedRates.map(r => r.rate);
  const statistics = {
    count: rateValues.length,
    min: rateValues.length > 0 ? Math.min(...rateValues) : null,
    max: rateValues.length > 0 ? Math.max(...rateValues) : null,
    average: rateValues.length > 0 ? rateValues.reduce((a, b) => a + b, 0) / rateValues.length : null,
    latest: rateValues.length > 0 ? rateValues[rateValues.length - 1] : null,
    oldest: rateValues.length > 0 ? rateValues[0] : null
  };
  
  logger.logCurrency('historical_rates_fetched', req.user?.id, {
    baseCurrency,
    targetCurrency,
    startDate: start_date,
    endDate: end_date,
    ratesCount: formattedRates.length
  });
  
  res.json({
    baseCurrency,
    targetCurrency,
    startDate: start_date,
    endDate: end_date,
    rates: formattedRates,
    statistics
  });
}));

/**
 * @route GET /api/currency/supported
 * @desc Get list of supported currencies
 * @access Public
 */
router.get('/supported', optionalAuthMiddleware, asyncHandler(async (req, res) => {
  const { data: currencies, error } = await req.supabase
    .from('exchange_rates')
    .select('target_currency')
    .eq('base_currency', 'KWD')
    .order('target_currency');
  
  if (error) {
    logger.error('Failed to fetch supported currencies', { 
      userId: req.user?.id,
      error: error.message 
    });
    throw new ValidationError('Failed to fetch supported currencies');
  }
  
  const supportedCurrencies = ['KWD', ...new Set(currencies?.map(c => c.target_currency) || [])];
  
  // Currency information (you might want to store this in a separate table)
  const currencyInfo = {
    'KWD': { name: 'Kuwaiti Dinar', symbol: 'د.ك', country: 'Kuwait' },
    'USD': { name: 'US Dollar', symbol: '$', country: 'United States' },
    'EUR': { name: 'Euro', symbol: '€', country: 'European Union' },
    'GBP': { name: 'British Pound', symbol: '£', country: 'United Kingdom' },
    'JPY': { name: 'Japanese Yen', symbol: '¥', country: 'Japan' },
    'AUD': { name: 'Australian Dollar', symbol: 'A$', country: 'Australia' },
    'CAD': { name: 'Canadian Dollar', symbol: 'C$', country: 'Canada' },
    'CHF': { name: 'Swiss Franc', symbol: 'CHF', country: 'Switzerland' },
    'CNY': { name: 'Chinese Yuan', symbol: '¥', country: 'China' },
    'INR': { name: 'Indian Rupee', symbol: '₹', country: 'India' },
    'SAR': { name: 'Saudi Riyal', symbol: 'ر.س', country: 'Saudi Arabia' },
    'AED': { name: 'UAE Dirham', symbol: 'د.إ', country: 'United Arab Emirates' },
    'QAR': { name: 'Qatari Riyal', symbol: 'ر.ق', country: 'Qatar' },
    'BHD': { name: 'Bahraini Dinar', symbol: '.د.ب', country: 'Bahrain' },
    'OMR': { name: 'Omani Rial', symbol: 'ر.ع.', country: 'Oman' }
  };
  
  const formattedCurrencies = supportedCurrencies.map(code => ({
    code,
    name: currencyInfo[code]?.name || code,
    symbol: currencyInfo[code]?.symbol || code,
    country: currencyInfo[code]?.country || 'Unknown'
  }));
  
  res.json({
    currencies: formattedCurrencies,
    baseCurrency: 'KWD',
    totalSupported: formattedCurrencies.length
  });
}));

module.exports = router;