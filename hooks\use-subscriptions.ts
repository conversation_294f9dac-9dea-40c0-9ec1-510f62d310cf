"use client"

import { useState, useEffect } from 'react'
import { db, realtime } from '@/lib/supabase'
import { useBusiness } from '@/contexts/business-context'
import { Database } from '@/types/database'

type Subscription = Database['public']['Tables']['subscriptions']['Row']
type SubscriptionInsert = Database['public']['Tables']['subscriptions']['Insert']
type SubscriptionUpdate = Database['public']['Tables']['subscriptions']['Update']

export function useSubscriptions() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const { currentBusiness } = useBusiness()

  useEffect(() => {
    if (currentBusiness) {
      loadSubscriptions()
      
      // Set up real-time subscription
      const subscription = realtime.subscribeToSubscriptions(
        currentBusiness.id,
        (payload) => {
          console.log('Real-time subscription update:', payload)
          
          switch (payload.eventType) {
            case 'INSERT':
              setSubscriptions(prev => [payload.new, ...prev])
              break
            case 'UPDATE':
              setSubscriptions(prev => 
                prev.map(sub => 
                  sub.id === payload.new.id ? payload.new : sub
                )
              )
              break
            case 'DELETE':
              setSubscriptions(prev => 
                prev.filter(sub => sub.id !== payload.old.id)
              )
              break
          }
        }
      )

      return () => {
        subscription.unsubscribe()
      }
    } else {
      setSubscriptions([])
      setLoading(false)
    }
  }, [currentBusiness])

  const loadSubscriptions = async () => {
    if (!currentBusiness) return

    try {
      setLoading(true)
      setError(null)
      
      const { data, error } = await db.subscriptions.getAll(currentBusiness.id)
      
      if (error) {
        setError(error.message)
        return
      }

      setSubscriptions(data || [])
    } catch (err) {
      setError('Failed to load subscriptions')
      console.error('Error loading subscriptions:', err)
    } finally {
      setLoading(false)
    }
  }

  const createSubscription = async (subscriptionData: Omit<SubscriptionInsert, 'business_id'>) => {
    if (!currentBusiness) throw new Error('No business selected')

    try {
      const { data, error } = await db.subscriptions.create({
        ...subscriptionData,
        business_id: currentBusiness.id,
      })

      if (error) throw error

      // Real-time will handle the update, but we can also update locally for immediate feedback
      setSubscriptions(prev => [data, ...prev])

      return data
    } catch (err) {
      console.error('Error creating subscription:', err)
      throw err
    }
  }

  const updateSubscription = async (id: string, updates: SubscriptionUpdate) => {
    try {
      const { data, error } = await db.subscriptions.update(id, updates)

      if (error) throw error

      // Real-time will handle the update, but we can also update locally for immediate feedback
      setSubscriptions(prev => 
        prev.map(subscription => 
          subscription.id === id ? data : subscription
        )
      )

      return data
    } catch (err) {
      console.error('Error updating subscription:', err)
      throw err
    }
  }

  const deleteSubscription = async (id: string) => {
    try {
      const { error } = await db.subscriptions.delete(id)

      if (error) throw error

      // Real-time will handle the update, but we can also update locally for immediate feedback
      setSubscriptions(prev => prev.filter(subscription => subscription.id !== id))
    } catch (err) {
      console.error('Error deleting subscription:', err)
      throw err
    }
  }

  const getActiveSubscriptions = () => {
    return subscriptions.filter(sub => sub.status === 'active')
  }

  const getUpcomingRenewals = (days: number = 7) => {
    const now = new Date()
    const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000)
    
    return subscriptions.filter(sub => {
      if (sub.status !== 'active') return false
      const renewalDate = new Date(sub.next_billing_date)
      return renewalDate >= now && renewalDate <= futureDate
    })
  }

  const getTotalMonthlySpend = () => {
    return subscriptions
      .filter(sub => sub.status === 'active')
      .reduce((total, sub) => {
        let monthlyAmount = sub.amount
        
        // Convert to monthly amount based on billing cycle
        switch (sub.billing_cycle) {
          case 'yearly':
            monthlyAmount = sub.amount / 12
            break
          case 'quarterly':
            monthlyAmount = sub.amount / 3
            break
          case 'weekly':
            monthlyAmount = sub.amount * 4.33 // Average weeks per month
            break
          // monthly is already correct
        }
        
        return total + monthlyAmount
      }, 0)
  }

  const refreshSubscriptions = async () => {
    await loadSubscriptions()
  }

  return {
    subscriptions,
    loading,
    error,
    createSubscription,
    updateSubscription,
    deleteSubscription,
    getActiveSubscriptions,
    getUpcomingRenewals,
    getTotalMonthlySpend,
    refreshSubscriptions,
  }
}
