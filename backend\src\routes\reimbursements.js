const express = require('express');
const { z } = require('zod');
const { as<PERSON><PERSON><PERSON><PERSON>, ValidationError, NotFoundError, ForbiddenError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const createReimbursementSchema = z.object({
  business_id: z.string().uuid('Invalid business ID'),
  payment_id: z.string().uuid('Invalid payment ID').optional(),
  employee_name: z.string().min(1, 'Employee name is required'),
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().length(3, 'Currency must be 3 characters').default('KWD'),
  original_amount: z.number().positive('Original amount must be positive').optional(),
  original_currency: z.string().length(3, 'Original currency must be 3 characters').optional(),
  exchange_rate: z.number().positive('Exchange rate must be positive').optional(),
  expense_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Expense date must be in YYYY-MM-DD format'),
  reimbursement_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Reimbursement date must be in YYYY-MM-DD format').optional(),
  category: z.string().min(1, 'Category is required'),
  description: z.string().min(1, 'Description is required'),
  receipt_url: z.string().url().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional().default([])
});

const updateReimbursementSchema = z.object({
  employee_name: z.string().min(1, 'Employee name is required').optional(),
  amount: z.number().positive('Amount must be positive').optional(),
  currency: z.string().length(3, 'Currency must be 3 characters').optional(),
  original_amount: z.number().positive('Original amount must be positive').optional(),
  original_currency: z.string().length(3, 'Original currency must be 3 characters').optional(),
  exchange_rate: z.number().positive('Exchange rate must be positive').optional(),
  expense_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Expense date must be in YYYY-MM-DD format').optional(),
  reimbursement_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Reimbursement date must be in YYYY-MM-DD format').optional(),
  category: z.string().min(1, 'Category is required').optional(),
  description: z.string().min(1, 'Description is required').optional(),
  receipt_url: z.string().url().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['pending', 'approved', 'rejected', 'paid']).optional()
});

/**
 * Middleware to verify business ownership
 */
const verifyBusinessOwnership = asyncHandler(async (req, res, next) => {
  const businessId = req.body.business_id || req.query.business_id;
  
  if (!businessId) {
    throw new ValidationError('Business ID is required');
  }

  const { data: business, error } = await req.supabase
    .from('businesses')
    .select('id, owner_id, name')
    .eq('id', businessId)
    .single();

  if (error || !business) {
    throw new NotFoundError('Business not found');
  }

  if (business.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this business');
  }

  req.business = business;
  next();
});

/**
 * Middleware to verify reimbursement ownership
 */
const verifyReimbursementOwnership = asyncHandler(async (req, res, next) => {
  const reimbursementId = req.params.id || req.params.reimbursementId;
  
  if (!reimbursementId) {
    throw new ValidationError('Reimbursement ID is required');
  }

  const { data: reimbursement, error } = await req.supabase
    .from('reimbursements')
    .select(`
      id, 
      business_id, 
      payment_id,
      employee_name,
      amount,
      currency,
      description,
      businesses!inner(id, owner_id, name)
    `)
    .eq('id', reimbursementId)
    .single();

  if (error || !reimbursement) {
    throw new NotFoundError('Reimbursement not found');
  }

  if (reimbursement.businesses.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this reimbursement');
  }

  req.reimbursement = reimbursement;
  next();
});

/**
 * @route POST /api/reimbursements
 * @desc Create a new reimbursement
 * @access Private
 */
router.post('/', verifyBusinessOwnership, asyncHandler(async (req, res) => {
  const validatedData = createReimbursementSchema.parse(req.body);
  
  // Verify payment belongs to the business if provided
  if (validatedData.payment_id) {
    const { data: payment, error: paymentError } = await req.supabase
      .from('payments')
      .select('id, business_id')
      .eq('id', validatedData.payment_id)
      .eq('business_id', validatedData.business_id)
      .single();

    if (paymentError || !payment) {
      throw new ValidationError('Payment not found or does not belong to this business');
    }
  }

  // Calculate exchange rate and converted amount if needed
  let finalAmount = validatedData.amount;
  let exchangeRate = validatedData.exchange_rate;
  
  if (validatedData.original_amount && validatedData.original_currency && 
      validatedData.original_currency !== validatedData.currency) {
    
    if (!exchangeRate) {
      // Fetch current exchange rate
      const { data: rateData } = await req.supabase
        .from('exchange_rates')
        .select('rate')
        .eq('base_currency', 'KWD')
        .eq('target_currency', validatedData.original_currency)
        .eq('date', new Date().toISOString().split('T')[0])
        .single();
      
      if (rateData) {
        exchangeRate = rateData.rate;
        finalAmount = validatedData.original_amount / exchangeRate;
      }
    } else {
      finalAmount = validatedData.original_amount / exchangeRate;
    }
  }

  const reimbursementData = {
    business_id: validatedData.business_id,
    payment_id: validatedData.payment_id,
    employee_name: validatedData.employee_name,
    amount: finalAmount,
    currency: validatedData.currency,
    original_amount: validatedData.original_amount,
    original_currency: validatedData.original_currency,
    exchange_rate: exchangeRate,
    expense_date: validatedData.expense_date,
    reimbursement_date: validatedData.reimbursement_date,
    category: validatedData.category,
    description: validatedData.description,
    receipt_url: validatedData.receipt_url,
    notes: validatedData.notes,
    tags: validatedData.tags,
    status: 'pending'
  };

  const { data: reimbursement, error } = await req.supabase
    .from('reimbursements')
    .insert(reimbursementData)
    .select()
    .single();

  if (error) {
    logger.error('Failed to create reimbursement', { 
      userId: req.user.id, 
      businessId: validatedData.business_id,
      error: error.message,
      reimbursementData: { 
        amount: validatedData.amount, 
        employee: validatedData.employee_name,
        description: validatedData.description 
      }
    });
    throw new ValidationError('Failed to create reimbursement');
  }

  logger.info('Reimbursement created', {
    userId: req.user.id,
    businessId: reimbursement.business_id,
    reimbursementId: reimbursement.id,
    employee: reimbursement.employee_name,
    amount: reimbursement.amount,
    currency: reimbursement.currency
  });

  res.status(201).json({
    message: 'Reimbursement created successfully',
    reimbursement: {
      id: reimbursement.id,
      businessId: reimbursement.business_id,
      paymentId: reimbursement.payment_id,
      employeeName: reimbursement.employee_name,
      amount: reimbursement.amount,
      currency: reimbursement.currency,
      originalAmount: reimbursement.original_amount,
      originalCurrency: reimbursement.original_currency,
      exchangeRate: reimbursement.exchange_rate,
      expenseDate: reimbursement.expense_date,
      reimbursementDate: reimbursement.reimbursement_date,
      category: reimbursement.category,
      description: reimbursement.description,
      receiptUrl: reimbursement.receipt_url,
      notes: reimbursement.notes,
      tags: reimbursement.tags,
      status: reimbursement.status,
      createdAt: reimbursement.created_at,
      updatedAt: reimbursement.updated_at
    }
  });
}));

/**
 * @route GET /api/reimbursements
 * @desc Get all reimbursements for a business
 * @access Private
 */
router.get('/', asyncHandler(async (req, res) => {
  const { 
    business_id, 
    page = 1, 
    limit = 10, 
    search, 
    status, 
    category, 
    employee,
    start_date,
    end_date,
    payment_id,
    sort_by = 'expense_date',
    sort_order = 'desc'
  } = req.query;

  if (!business_id) {
    throw new ValidationError('Business ID is required');
  }

  // Verify business ownership
  const { data: business, error: businessError } = await req.supabase
    .from('businesses')
    .select('id, owner_id')
    .eq('id', business_id)
    .single();

  if (businessError || !business) {
    throw new NotFoundError('Business not found');
  }

  if (business.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this business');
  }

  const offset = (page - 1) * limit;
  const validSortColumns = ['amount', 'expense_date', 'reimbursement_date', 'created_at', 'status', 'employee_name'];
  const sortColumn = validSortColumns.includes(sort_by) ? sort_by : 'expense_date';
  const sortDirection = sort_order === 'asc' ? true : false;

  let query = req.supabase
    .from('reimbursements')
    .select(`
      *,
      payments(id, description, amount as payment_amount)
    `, { count: 'exact' })
    .eq('business_id', business_id)
    .order(sortColumn, { ascending: sortDirection })
    .range(offset, offset + limit - 1);

  // Apply filters
  if (search) {
    query = query.or(`description.ilike.%${search}%,employee_name.ilike.%${search}%,category.ilike.%${search}%`);
  }

  if (status) {
    query = query.eq('status', status);
  }

  if (category) {
    query = query.eq('category', category);
  }

  if (employee) {
    query = query.ilike('employee_name', `%${employee}%`);
  }

  if (payment_id) {
    query = query.eq('payment_id', payment_id);
  }

  if (start_date) {
    query = query.gte('expense_date', start_date);
  }

  if (end_date) {
    query = query.lte('expense_date', end_date);
  }

  const { data: reimbursements, error, count } = await query;

  if (error) {
    logger.error('Failed to fetch reimbursements', { 
      userId: req.user.id, 
      businessId: business_id,
      error: error.message 
    });
    throw new ValidationError('Failed to fetch reimbursements');
  }

  const totalPages = Math.ceil(count / limit);

  res.json({
    reimbursements: reimbursements.map(reimbursement => ({
      id: reimbursement.id,
      businessId: reimbursement.business_id,
      paymentId: reimbursement.payment_id,
      paymentDescription: reimbursement.payments?.description,
      paymentAmount: reimbursement.payments?.payment_amount,
      employeeName: reimbursement.employee_name,
      amount: reimbursement.amount,
      currency: reimbursement.currency,
      originalAmount: reimbursement.original_amount,
      originalCurrency: reimbursement.original_currency,
      exchangeRate: reimbursement.exchange_rate,
      expenseDate: reimbursement.expense_date,
      reimbursementDate: reimbursement.reimbursement_date,
      category: reimbursement.category,
      description: reimbursement.description,
      receiptUrl: reimbursement.receipt_url,
      notes: reimbursement.notes,
      tags: reimbursement.tags,
      status: reimbursement.status,
      createdAt: reimbursement.created_at,
      updatedAt: reimbursement.updated_at
    })),
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: count,
      itemsPerPage: parseInt(limit),
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    }
  });
}));

/**
 * @route GET /api/reimbursements/:id
 * @desc Get a specific reimbursement
 * @access Private
 */
router.get('/:id', verifyReimbursementOwnership, asyncHandler(async (req, res) => {
  const { data: reimbursement, error } = await req.supabase
    .from('reimbursements')
    .select(`
      *,
      payments(id, description, amount, payment_date),
      businesses(id, name)
    `)
    .eq('id', req.params.id)
    .single();

  if (error) {
    logger.error('Failed to fetch reimbursement', { 
      userId: req.user.id, 
      reimbursementId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to fetch reimbursement');
  }

  res.json({
    reimbursement: {
      id: reimbursement.id,
      businessId: reimbursement.business_id,
      businessName: reimbursement.businesses?.name,
      paymentId: reimbursement.payment_id,
      paymentDescription: reimbursement.payments?.description,
      paymentAmount: reimbursement.payments?.amount,
      paymentDate: reimbursement.payments?.payment_date,
      employeeName: reimbursement.employee_name,
      amount: reimbursement.amount,
      currency: reimbursement.currency,
      originalAmount: reimbursement.original_amount,
      originalCurrency: reimbursement.original_currency,
      exchangeRate: reimbursement.exchange_rate,
      expenseDate: reimbursement.expense_date,
      reimbursementDate: reimbursement.reimbursement_date,
      category: reimbursement.category,
      description: reimbursement.description,
      receiptUrl: reimbursement.receipt_url,
      notes: reimbursement.notes,
      tags: reimbursement.tags,
      status: reimbursement.status,
      createdAt: reimbursement.created_at,
      updatedAt: reimbursement.updated_at
    }
  });
}));

/**
 * @route PUT /api/reimbursements/:id
 * @desc Update a reimbursement
 * @access Private
 */
router.put('/:id', verifyReimbursementOwnership, asyncHandler(async (req, res) => {
  const validatedData = updateReimbursementSchema.parse(req.body);
  
  // Remove undefined values
  const updateData = Object.fromEntries(
    Object.entries(validatedData).filter(([_, value]) => value !== undefined)
  );

  if (Object.keys(updateData).length === 0) {
    throw new ValidationError('No valid fields to update');
  }

  // Recalculate amount if exchange rate or original amount changed
  if ((updateData.original_amount || updateData.exchange_rate) && 
      req.reimbursement.original_currency && req.reimbursement.original_currency !== req.reimbursement.currency) {
    
    const originalAmount = updateData.original_amount || req.reimbursement.original_amount;
    const exchangeRate = updateData.exchange_rate || req.reimbursement.exchange_rate;
    
    if (originalAmount && exchangeRate) {
      updateData.amount = originalAmount / exchangeRate;
    }
  }

  const { data: reimbursement, error } = await req.supabase
    .from('reimbursements')
    .update(updateData)
    .eq('id', req.params.id)
    .select()
    .single();

  if (error) {
    logger.error('Failed to update reimbursement', { 
      userId: req.user.id, 
      reimbursementId: req.params.id,
      error: error.message,
      updateData
    });
    throw new ValidationError('Failed to update reimbursement');
  }

  logger.info('Reimbursement updated', {
    userId: req.user.id,
    businessId: reimbursement.business_id,
    reimbursementId: reimbursement.id,
    employee: reimbursement.employee_name,
    amount: reimbursement.amount,
    currency: reimbursement.currency,
    updatedFields: Object.keys(updateData)
  });

  res.json({
    message: 'Reimbursement updated successfully',
    reimbursement: {
      id: reimbursement.id,
      businessId: reimbursement.business_id,
      paymentId: reimbursement.payment_id,
      employeeName: reimbursement.employee_name,
      amount: reimbursement.amount,
      currency: reimbursement.currency,
      originalAmount: reimbursement.original_amount,
      originalCurrency: reimbursement.original_currency,
      exchangeRate: reimbursement.exchange_rate,
      expenseDate: reimbursement.expense_date,
      reimbursementDate: reimbursement.reimbursement_date,
      category: reimbursement.category,
      description: reimbursement.description,
      receiptUrl: reimbursement.receipt_url,
      notes: reimbursement.notes,
      tags: reimbursement.tags,
      status: reimbursement.status,
      createdAt: reimbursement.created_at,
      updatedAt: reimbursement.updated_at
    }
  });
}));

/**
 * @route DELETE /api/reimbursements/:id
 * @desc Delete a reimbursement
 * @access Private
 */
router.delete('/:id', verifyReimbursementOwnership, asyncHandler(async (req, res) => {
  const { error } = await req.supabase
    .from('reimbursements')
    .delete()
    .eq('id', req.params.id);

  if (error) {
    logger.error('Failed to delete reimbursement', { 
      userId: req.user.id, 
      reimbursementId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to delete reimbursement');
  }

  logger.info('Reimbursement deleted', {
    userId: req.user.id,
    businessId: req.reimbursement.business_id,
    reimbursementId: req.params.id,
    employee: req.reimbursement.employee_name,
    amount: req.reimbursement.amount,
    currency: req.reimbursement.currency
  });

  res.json({
    message: 'Reimbursement deleted successfully'
  });
}));

/**
 * @route PUT /api/reimbursements/:id/approve
 * @desc Approve a reimbursement
 * @access Private
 */
router.put('/:id/approve', verifyReimbursementOwnership, asyncHandler(async (req, res) => {
  const { reimbursement_date } = req.body;
  
  const updateData = {
    status: 'approved'
  };
  
  if (reimbursement_date) {
    updateData.reimbursement_date = reimbursement_date;
  }

  const { data: reimbursement, error } = await req.supabase
    .from('reimbursements')
    .update(updateData)
    .eq('id', req.params.id)
    .select()
    .single();

  if (error) {
    logger.error('Failed to approve reimbursement', { 
      userId: req.user.id, 
      reimbursementId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to approve reimbursement');
  }

  logger.info('Reimbursement approved', {
    userId: req.user.id,
    businessId: reimbursement.business_id,
    reimbursementId: reimbursement.id,
    employee: reimbursement.employee_name,
    amount: reimbursement.amount,
    currency: reimbursement.currency
  });

  res.json({
    message: 'Reimbursement approved successfully',
    reimbursement: {
      id: reimbursement.id,
      status: reimbursement.status,
      reimbursementDate: reimbursement.reimbursement_date,
      updatedAt: reimbursement.updated_at
    }
  });
}));

/**
 * @route PUT /api/reimbursements/:id/reject
 * @desc Reject a reimbursement
 * @access Private
 */
router.put('/:id/reject', verifyReimbursementOwnership, asyncHandler(async (req, res) => {
  const { notes } = req.body;
  
  const updateData = {
    status: 'rejected'
  };
  
  if (notes) {
    updateData.notes = notes;
  }

  const { data: reimbursement, error } = await req.supabase
    .from('reimbursements')
    .update(updateData)
    .eq('id', req.params.id)
    .select()
    .single();

  if (error) {
    logger.error('Failed to reject reimbursement', { 
      userId: req.user.id, 
      reimbursementId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to reject reimbursement');
  }

  logger.info('Reimbursement rejected', {
    userId: req.user.id,
    businessId: reimbursement.business_id,
    reimbursementId: reimbursement.id,
    employee: reimbursement.employee_name,
    amount: reimbursement.amount,
    currency: reimbursement.currency
  });

  res.json({
    message: 'Reimbursement rejected successfully',
    reimbursement: {
      id: reimbursement.id,
      status: reimbursement.status,
      notes: reimbursement.notes,
      updatedAt: reimbursement.updated_at
    }
  });
}));

/**
 * @route PUT /api/reimbursements/:id/pay
 * @desc Mark a reimbursement as paid
 * @access Private
 */
router.put('/:id/pay', verifyReimbursementOwnership, asyncHandler(async (req, res) => {
  const { reimbursement_date } = req.body;
  
  // Check if reimbursement is approved
  if (req.reimbursement.status !== 'approved') {
    throw new ValidationError('Reimbursement must be approved before it can be marked as paid');
  }
  
  const updateData = {
    status: 'paid',
    reimbursement_date: reimbursement_date || new Date().toISOString().split('T')[0]
  };

  const { data: reimbursement, error } = await req.supabase
    .from('reimbursements')
    .update(updateData)
    .eq('id', req.params.id)
    .select()
    .single();

  if (error) {
    logger.error('Failed to mark reimbursement as paid', { 
      userId: req.user.id, 
      reimbursementId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to mark reimbursement as paid');
  }

  logger.info('Reimbursement marked as paid', {
    userId: req.user.id,
    businessId: reimbursement.business_id,
    reimbursementId: reimbursement.id,
    employee: reimbursement.employee_name,
    amount: reimbursement.amount,
    currency: reimbursement.currency
  });

  res.json({
    message: 'Reimbursement marked as paid successfully',
    reimbursement: {
      id: reimbursement.id,
      status: reimbursement.status,
      reimbursementDate: reimbursement.reimbursement_date,
      updatedAt: reimbursement.updated_at
    }
  });
}));

/**
 * @route GET /api/reimbursements/summary/:business_id
 * @desc Get reimbursement summary for a business
 * @access Private
 */
router.get('/summary/:business_id', asyncHandler(async (req, res) => {
  const { business_id } = req.params;
  const { period = '30d' } = req.query;
  
  // Verify business ownership
  const { data: business, error: businessError } = await req.supabase
    .from('businesses')
    .select('id, owner_id')
    .eq('id', business_id)
    .single();

  if (businessError || !business) {
    throw new NotFoundError('Business not found');
  }

  if (business.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this business');
  }

  // Calculate date range
  const now = new Date();
  let startDate;
  
  switch (period) {
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case '1y':
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  const { data: reimbursements, error } = await req.supabase
    .from('reimbursements')
    .select('amount, currency, expense_date, status, category, employee_name')
    .eq('business_id', business_id)
    .gte('expense_date', startDate.toISOString().split('T')[0]);

  if (error) {
    logger.error('Failed to fetch reimbursement summary', { 
      userId: req.user.id, 
      businessId: business_id,
      error: error.message 
    });
    throw new ValidationError('Failed to fetch reimbursement summary');
  }

  // Calculate summary statistics
  const summary = {
    totalReimbursements: reimbursements?.length || 0,
    totalAmount: reimbursements?.reduce((sum, r) => sum + parseFloat(r.amount || 0), 0) || 0,
    pendingReimbursements: reimbursements?.filter(r => r.status === 'pending').length || 0,
    approvedReimbursements: reimbursements?.filter(r => r.status === 'approved').length || 0,
    rejectedReimbursements: reimbursements?.filter(r => r.status === 'rejected').length || 0,
    paidReimbursements: reimbursements?.filter(r => r.status === 'paid').length || 0,
    byCategory: {},
    byEmployee: {},
    byMonth: {},
    period: {
      startDate: startDate.toISOString().split('T')[0],
      endDate: now.toISOString().split('T')[0],
      days: Math.ceil((now - startDate) / (1000 * 60 * 60 * 24))
    }
  };

  // Group by category
  reimbursements?.forEach(reimbursement => {
    const category = reimbursement.category || 'Uncategorized';
    if (!summary.byCategory[category]) {
      summary.byCategory[category] = { count: 0, amount: 0 };
    }
    summary.byCategory[category].count++;
    summary.byCategory[category].amount += parseFloat(reimbursement.amount || 0);
  });

  // Group by employee
  reimbursements?.forEach(reimbursement => {
    const employee = reimbursement.employee_name || 'Unknown';
    if (!summary.byEmployee[employee]) {
      summary.byEmployee[employee] = { count: 0, amount: 0 };
    }
    summary.byEmployee[employee].count++;
    summary.byEmployee[employee].amount += parseFloat(reimbursement.amount || 0);
  });

  // Group by month
  reimbursements?.forEach(reimbursement => {
    const month = reimbursement.expense_date.substring(0, 7); // YYYY-MM
    if (!summary.byMonth[month]) {
      summary.byMonth[month] = { count: 0, amount: 0 };
    }
    summary.byMonth[month].count++;
    summary.byMonth[month].amount += parseFloat(reimbursement.amount || 0);
  });

  res.json({ summary });
}));

module.exports = router;