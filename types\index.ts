// Shared type definitions for the subscription dashboard application

export interface Subscription {
  id: string
  name: string
  category: string
  amount: number
  currency: "USD" | "GBP"
  kwdAmount: number
  exchangeRate: number
  billingCycle: "monthly" | "yearly" | "quarterly"
  nextPaymentDate: string
  status: "active" | "cancelled" | "paused"
  description: string
  createdAt: string
  lastUpdated: string
}

export interface PaymentRecord {
  id: string
  subscriptionName: string
  originalAmount: number
  originalCurrency: "USD" | "GBP"
  kwdAmount: number
  exchangeRate: number
  conversionTimestamp: string
  paymentDate: string
  description: string
  receiptUrl?: string
  autoCreateReimbursement: boolean
  createdAt: string
}

export interface ReimbursementRecord {
  id: string
  linkedPaymentId: string
  kwdAmount: number
  status: "pending" | "approved" | "rejected"
  requestDate: string
  approvalDate?: string
  notes: string
  createdAt: string
}

export interface UserProfile {
  firstName: string
  lastName: string
  email: string
  phone: string
  address: string
  city: string
  country: string
  bio: string
  avatar: string | null
  dateOfBirth: string
  jobTitle: string
  company: string
  createdAt: string
  lastUpdated: string
}

export interface CurrencyRates {
  usdToKwd: number
  gbpToKwd: number
  lastUpdated: string
}

export interface AppSettings {
  notifications: {
    emailNotifications: boolean
    pushNotifications: boolean
    paymentReminders: boolean
    weeklyReports: boolean
    currencyAlerts: boolean
  }
  preferences: {
    defaultCurrency: "USD" | "GBP"
    dateFormat: "MM/DD/YYYY" | "DD/MM/YYYY" | "YYYY-MM-DD"
    timeFormat: "12h" | "24h"
    language: string
    autoRefreshRates: boolean
    compactView: boolean
  }
  privacy: {
    shareUsageData: boolean
    allowAnalytics: boolean
    showOnlineStatus: boolean
  }
  lastUpdated: string
}

// Form data types
export interface PaymentFormData {
  subscriptionName: string
  amount: string
  currency: "USD" | "GBP"
  paymentDate: Date
  description: string
  receipt: File | null
  autoCreateReimbursement: boolean
}

export interface SubscriptionFormData {
  name: string
  category: string
  amount: string
  currency: "USD" | "GBP"
  billingCycle: "monthly" | "yearly" | "quarterly"
  nextPaymentDate: Date
  status: "active" | "cancelled" | "paused"
  description: string
}

export interface SubscriptionFormState {
  name: string
  category: string
  amount: string
  currency: "USD" | "GBP"
  billingCycle: "monthly" | "yearly" | "quarterly"
  nextPaymentDate: Date
  status: "active" | "cancelled" | "paused"
  description: string
}

// API Response types
export interface CurrencyApiResponse {
  kwd: number
}

// Utility types
export type Currency = "USD" | "GBP" | "KWD"
export type BillingCycle = "monthly" | "yearly" | "quarterly"
export type SubscriptionStatus = "active" | "cancelled" | "paused"
export type ReimbursementStatus = "pending" | "approved" | "rejected"
export type DateFormat = "MM/DD/YYYY" | "DD/MM/YYYY" | "YYYY-MM-DD"
export type TimeFormat = "12h" | "24h"

// Component prop types
export interface SharedLayoutProps {
  activeUrl?: string
  children: React.ReactNode
}

export interface DashboardLayoutProps {
  activeUrl?: string
}

// Hook return types
export interface UseCurrencyRatesReturn {
  rates: CurrencyRates
  isLoading: boolean
  error: string | null
  isOnline: boolean
  refreshRates: () => void
}

// Constants
export const SUBSCRIPTION_CATEGORIES = [
  "Entertainment",
  "Productivity", 
  "Cloud Storage",
  "Development Tools",
  "Design",
  "Communication",
  "Finance",
  "Health & Fitness",
  "Education",
  "Other",
] as const

export type SubscriptionCategory = typeof SUBSCRIPTION_CATEGORIES[number]

// Dashboard data types
export interface DashboardData {
  activeSubscriptions: { count: number; status: string }
  upcomingPayments: {
    usd: number
    gbp: number
    kwd: number
    status: string
  }
  pendingReimbursements: { amount: number; status: string }
  totalSpentThisMonth: { amount: number; status: string }
}

// Chart data types
export interface ChartDataPoint {
  month: string
  totalKwd: number
  count: number
}

export interface CategoryBreakdown {
  [category: string]: number
}

// Error types
export interface AppError {
  message: string
  code?: string
  details?: any
}

// Local storage keys
export const STORAGE_KEYS = {
  SUBSCRIPTIONS: 'subscriptions',
  PAYMENTS: 'payments', 
  REIMBURSEMENTS: 'reimbursements',
  USER_PROFILE: 'userProfile',
  APP_SETTINGS: 'appSettings',
  CURRENCY_RATES_CACHE: 'currency_rates_cache',
} as const

// API endpoints (for future backend integration)
export const API_ENDPOINTS = {
  SUBSCRIPTIONS: '/api/subscriptions',
  PAYMENTS: '/api/payments',
  REIMBURSEMENTS: '/api/reimbursements',
  CURRENCY_RATES: '/api/currency/rates',
  USER_PROFILE: '/api/user/profile',
  SETTINGS: '/api/user/settings',
} as const
