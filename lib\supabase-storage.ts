"use client"

import { supabase, db } from '@/lib/supabase'
import { Database } from '@/types/database'
import { 
  Subscription, 
  PaymentRecord, 
  ReimbursementRecord, 
  UserProfile, 
  AppSettings, 
  CurrencyRates 
} from '@/types'

// Type aliases for database types
type DbProfile = Database['public']['Tables']['profiles']['Row']
type DbSubscription = Database['public']['Tables']['subscriptions']['Row']
type DbPayment = Database['public']['Tables']['payments']['Row']
type DbReimbursement = Database['public']['Tables']['reimbursements']['Row']
type DbBusiness = Database['public']['Tables']['businesses']['Row']

// Helper function to get current user
const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error || !user) {
    throw new Error('User not authenticated')
  }
  return user
}

// Helper function to get user's business (create if doesn't exist)
const getUserBusiness = async (userId: string): Promise<DbBusiness> => {
  // First try to get existing business
  const { data: business, error } = await supabase
    .from('businesses')
    .select('*')
    .eq('owner_id', userId)
    .single()

  if (business) {
    return business
  }

  // If no business exists, create a default one
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()

  const businessName = profile 
    ? `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'My Business'
    : 'My Business'

  const { data: newBusiness, error: createError } = await supabase
    .from('businesses')
    .insert({
      owner_id: userId,
      name: businessName,
      currency: 'KWD',
      country: 'Kuwait'
    })
    .select()
    .single()

  if (createError || !newBusiness) {
    throw new Error('Failed to create business')
  }

  return newBusiness
}

// Convert database types to app types
const convertDbSubscriptionToApp = (dbSub: DbSubscription): Subscription => ({
  id: dbSub.id,
  name: dbSub.name,
  description: dbSub.description || '',
  amount: dbSub.amount,
  currency: dbSub.currency as 'USD' | 'GBP',
  billingCycle: dbSub.billing_cycle as 'weekly' | 'monthly' | 'quarterly' | 'yearly',
  startDate: dbSub.start_date,
  endDate: dbSub.end_date || undefined,
  nextBillingDate: dbSub.next_billing_date,
  status: dbSub.status as 'active' | 'paused' | 'cancelled',
  category: dbSub.category || '',
  vendor: dbSub.vendor || '',
  website: dbSub.website || '',
  paymentMethod: dbSub.payment_method || '',
  autoRenew: dbSub.auto_renew,
  notes: dbSub.notes || '',
  tags: dbSub.tags || [],
  createdAt: dbSub.created_at,
  lastUpdated: dbSub.updated_at
})

const convertDbPaymentToApp = (dbPayment: DbPayment): PaymentRecord => ({
  id: dbPayment.id,
  subscriptionName: '', // We'll need to join with subscriptions to get this
  subscriptionId: dbPayment.subscription_id || '',
  amount: dbPayment.amount,
  currency: dbPayment.currency as 'USD' | 'GBP',
  kwdAmount: dbPayment.kwd_amount || 0,
  exchangeRate: dbPayment.exchange_rate || 0,
  paymentDate: dbPayment.payment_date,
  paymentMethod: dbPayment.payment_method || '',
  description: dbPayment.description || '',
  receipt: dbPayment.receipt_url || null,
  autoCreateReimbursement: dbPayment.auto_create_reimbursement,
  createdAt: dbPayment.created_at
})

const convertDbReimbursementToApp = (dbReimb: DbReimbursement): ReimbursementRecord => ({
  id: dbReimb.id,
  linkedPaymentId: dbReimb.payment_id || '',
  kwdAmount: dbReimb.amount,
  status: dbReimb.status as 'pending' | 'approved' | 'rejected',
  requestDate: dbReimb.submitted_date || dbReimb.created_at,
  approvalDate: dbReimb.approved_date || undefined,
  notes: dbReimb.notes || '',
  createdAt: dbReimb.created_at
})

const convertDbProfileToApp = (dbProfile: DbProfile): UserProfile => ({
  firstName: dbProfile.first_name || '',
  lastName: dbProfile.last_name || '',
  email: dbProfile.email,
  phone: dbProfile.phone || '',
  address: '', // Not in database schema
  city: '', // Not in database schema
  country: '', // Not in database schema
  bio: '', // Not in database schema
  avatar: dbProfile.avatar_url,
  dateOfBirth: '', // Not in database schema
  jobTitle: '', // Not in database schema
  company: '', // Not in database schema
  createdAt: dbProfile.created_at,
  lastUpdated: dbProfile.updated_at
})

// Subscription storage operations
export const SupabaseSubscriptionStorage = {
  get: async (): Promise<Subscription[]> => {
    try {
      const user = await getCurrentUser()
      const business = await getUserBusiness(user.id)
      
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('business_id', business.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data?.map(convertDbSubscriptionToApp) || []
    } catch (error) {
      console.error('Error fetching subscriptions:', error)
      return []
    }
  },

  add: async (subscription: Subscription): Promise<void> => {
    try {
      const user = await getCurrentUser()
      const business = await getUserBusiness(user.id)
      
      const { error } = await supabase
        .from('subscriptions')
        .insert({
          business_id: business.id,
          name: subscription.name,
          description: subscription.description,
          amount: subscription.amount,
          currency: subscription.currency,
          billing_cycle: subscription.billingCycle,
          start_date: subscription.startDate,
          end_date: subscription.endDate || null,
          next_billing_date: subscription.nextBillingDate,
          status: subscription.status,
          category: subscription.category,
          vendor: subscription.vendor,
          website: subscription.website,
          payment_method: subscription.paymentMethod,
          auto_renew: subscription.autoRenew,
          notes: subscription.notes,
          tags: subscription.tags
        })

      if (error) throw error
    } catch (error) {
      console.error('Error adding subscription:', error)
      throw error
    }
  },

  update: async (id: string, subscription: Subscription): Promise<void> => {
    try {
      const { error } = await supabase
        .from('subscriptions')
        .update({
          name: subscription.name,
          description: subscription.description,
          amount: subscription.amount,
          currency: subscription.currency,
          billing_cycle: subscription.billingCycle,
          start_date: subscription.startDate,
          end_date: subscription.endDate || null,
          next_billing_date: subscription.nextBillingDate,
          status: subscription.status,
          category: subscription.category,
          vendor: subscription.vendor,
          website: subscription.website,
          payment_method: subscription.paymentMethod,
          auto_renew: subscription.autoRenew,
          notes: subscription.notes,
          tags: subscription.tags,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error updating subscription:', error)
      throw error
    }
  },

  remove: async (id: string): Promise<void> => {
    try {
      const { error } = await supabase
        .from('subscriptions')
        .delete()
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error removing subscription:', error)
      throw error
    }
  }
}

// Payment storage operations
export const SupabasePaymentStorage = {
  get: async (): Promise<PaymentRecord[]> => {
    try {
      const user = await getCurrentUser()
      const business = await getUserBusiness(user.id)
      
      const { data, error } = await supabase
        .from('payments')
        .select(`
          *,
          subscriptions(name)
        `)
        .eq('business_id', business.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      
      return data?.map((payment: any) => ({
        ...convertDbPaymentToApp(payment),
        subscriptionName: payment.subscriptions?.name || 'Unknown'
      })) || []
    } catch (error) {
      console.error('Error fetching payments:', error)
      return []
    }
  },

  add: async (payment: PaymentRecord): Promise<void> => {
    try {
      const user = await getCurrentUser()
      const business = await getUserBusiness(user.id)

      const { error } = await supabase
        .from('payments')
        .insert({
          business_id: business.id,
          subscription_id: payment.subscriptionId || null,
          amount: payment.amount,
          currency: payment.currency,
          kwd_amount: payment.kwdAmount,
          exchange_rate: payment.exchangeRate,
          payment_date: payment.paymentDate,
          payment_method: payment.paymentMethod,
          description: payment.description,
          receipt_url: payment.receipt,
          auto_create_reimbursement: payment.autoCreateReimbursement,
          status: 'completed'
        })

      if (error) throw error
    } catch (error) {
      console.error('Error adding payment:', error)
      throw error
    }
  }
}

// Reimbursement storage operations
export const SupabaseReimbursementStorage = {
  get: async (): Promise<ReimbursementRecord[]> => {
    try {
      const user = await getCurrentUser()
      const business = await getUserBusiness(user.id)

      const { data, error } = await supabase
        .from('reimbursements')
        .select('*')
        .eq('business_id', business.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data?.map(convertDbReimbursementToApp) || []
    } catch (error) {
      console.error('Error fetching reimbursements:', error)
      return []
    }
  },

  add: async (reimbursement: ReimbursementRecord): Promise<void> => {
    try {
      const user = await getCurrentUser()
      const business = await getUserBusiness(user.id)

      const { error } = await supabase
        .from('reimbursements')
        .insert({
          business_id: business.id,
          payment_id: reimbursement.linkedPaymentId || null,
          amount: reimbursement.kwdAmount,
          currency: 'KWD',
          description: reimbursement.notes,
          status: reimbursement.status,
          submitted_date: reimbursement.requestDate,
          notes: reimbursement.notes
        })

      if (error) throw error
    } catch (error) {
      console.error('Error adding reimbursement:', error)
      throw error
    }
  }
}

// Profile storage operations
export const SupabaseProfileStorage = {
  get: async (): Promise<UserProfile | null> => {
    try {
      const user = await getCurrentUser()

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error) throw error
      return data ? convertDbProfileToApp(data) : null
    } catch (error) {
      console.error('Error fetching profile:', error)
      return null
    }
  },

  set: async (profile: UserProfile): Promise<void> => {
    try {
      const user = await getCurrentUser()

      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: profile.firstName,
          last_name: profile.lastName,
          phone: profile.phone,
          avatar_url: profile.avatar,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)

      if (error) throw error
    } catch (error) {
      console.error('Error updating profile:', error)
      throw error
    }
  }
}

// Settings storage (we'll store this as JSON in the profiles table or create a separate settings table)
export const SupabaseSettingsStorage = {
  get: async (): Promise<AppSettings | null> => {
    // For now, return null as settings aren't in the database schema
    // This could be extended to store settings in a separate table or as JSON in profiles
    return null
  },

  set: async (settings: AppSettings): Promise<void> => {
    // For now, do nothing as settings aren't in the database schema
    console.log('Settings storage not implemented yet')
  }
}

// Currency rates storage - uses localStorage as fallback since this is cache data
export const SupabaseCurrencyRatesStorage = {
  get: async (): Promise<{ rates: CurrencyRates; timestamp: string } | null> => {
    // First try to get from Supabase database
    try {
      const { data, error } = await supabase
        .from('exchange_rates')
        .select('*')
        .in('from_currency', ['USD', 'GBP'])
        .eq('to_currency', 'KWD')
        .order('created_at', { ascending: false })
        .limit(2)

      if (!error && data && data.length >= 2) {
        const usdRate = data.find(r => r.from_currency === 'USD')
        const gbpRate = data.find(r => r.from_currency === 'GBP')

        if (usdRate && gbpRate) {
          return {
            rates: {
              usdToKwd: usdRate.rate,
              gbpToKwd: gbpRate.rate,
              lastUpdated: usdRate.created_at
            },
            timestamp: usdRate.created_at
          }
        }
      }
    } catch (error) {
      console.info('Database currency rates not available, using localStorage fallback')
    }

    // Fallback to localStorage for currency rates cache
    try {
      if (typeof window !== 'undefined') {
        const cached = localStorage.getItem('currency_rates_cache')
        if (cached) {
          return JSON.parse(cached)
        }
      }
    } catch (error) {
      console.warn('Error reading currency rates from localStorage:', error)
    }

    return null
  },

  set: async (rates: CurrencyRates, timestamp: string): Promise<void> => {
    // Always save to localStorage as fallback cache
    try {
      if (typeof window !== 'undefined') {
        const cacheData = { rates, timestamp }
        localStorage.setItem('currency_rates_cache', JSON.stringify(cacheData))
      }
    } catch (error) {
      console.warn('Error saving currency rates to localStorage:', error)
    }

    // Try to save to Supabase database (optional)
    try {
      const date = new Date().toISOString().split('T')[0]

      // Try to insert USD rate
      const { error: usdError } = await supabase
        .from('exchange_rates')
        .upsert({
          from_currency: 'USD',
          to_currency: 'KWD',
          rate: rates.usdToKwd,
          date: date,
          source: 'api'
        }, {
          onConflict: 'from_currency,to_currency,date'
        })

      // Try to insert GBP rate
      const { error: gbpError } = await supabase
        .from('exchange_rates')
        .upsert({
          from_currency: 'GBP',
          to_currency: 'KWD',
          rate: rates.gbpToKwd,
          date: date,
          source: 'api'
        }, {
          onConflict: 'from_currency,to_currency,date'
        })

      // Log database save status but don't fail if it doesn't work
      if (!usdError && !gbpError) {
        console.info('Currency rates saved to database successfully')
      } else {
        console.info('Currency rates saved to localStorage cache (database not available)')
      }
    } catch (error) {
      console.info('Currency rates saved to localStorage cache (database not available)')
    }
  }
}

// Main storage export that replaces the localStorage Storage
export const SupabaseStorage = {
  subscriptions: SupabaseSubscriptionStorage,
  payments: SupabasePaymentStorage,
  reimbursements: SupabaseReimbursementStorage,
  profile: SupabaseProfileStorage,
  settings: SupabaseSettingsStorage,
  currencyRates: SupabaseCurrencyRatesStorage
}
