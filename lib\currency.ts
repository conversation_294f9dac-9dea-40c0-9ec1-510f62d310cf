import { db } from '@/lib/supabase'

// Default exchange rates (fallback values)
const DEFAULT_RATES = {
  'USD-KWD': 0.31,
  'GBP-KWD': 0.38,
  'KWD-USD': 3.25,
  'KWD-GBP': 2.63,
}

export interface ExchangeRate {
  from_currency: string
  to_currency: string
  rate: number
  date: string
  source?: string
}

export class CurrencyConverter {
  private static rateCache: Map<string, { rate: number; timestamp: number }> = new Map()
  private static readonly CACHE_DURATION = 1000 * 60 * 60 // 1 hour

  /**
   * Get exchange rate from cache or database
   */
  static async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<number> {
    if (fromCurrency === toCurrency) return 1

    const cacheKey = `${fromCurrency}-${toCurrency}`
    const cached = this.rateCache.get(cacheKey)
    
    // Check if we have a valid cached rate
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.rate
    }

    try {
      // Try to get the latest rate from database
      const { data, error } = await db.exchangeRates.getLatest(fromCurrency, toCurrency)
      
      if (!error && data) {
        const rate = data.rate
        this.rateCache.set(cacheKey, { rate, timestamp: Date.now() })
        return rate
      }
    } catch (error) {
      console.warn('Failed to fetch exchange rate from database:', error)
    }

    // Fall back to default rates
    const defaultRate = DEFAULT_RATES[cacheKey as keyof typeof DEFAULT_RATES]
    if (defaultRate) {
      this.rateCache.set(cacheKey, { rate: defaultRate, timestamp: Date.now() })
      return defaultRate
    }

    // If no default rate available, try inverse rate
    const inverseKey = `${toCurrency}-${fromCurrency}`
    const inverseRate = DEFAULT_RATES[inverseKey as keyof typeof DEFAULT_RATES]
    if (inverseRate) {
      const rate = 1 / inverseRate
      this.rateCache.set(cacheKey, { rate, timestamp: Date.now() })
      return rate
    }

    throw new Error(`No exchange rate available for ${fromCurrency} to ${toCurrency}`)
  }

  /**
   * Convert amount from one currency to another
   */
  static async convert(
    amount: number,
    fromCurrency: string,
    toCurrency: string
  ): Promise<{ convertedAmount: number; rate: number }> {
    const rate = await this.getExchangeRate(fromCurrency, toCurrency)
    const convertedAmount = amount * rate
    
    return {
      convertedAmount: Math.round(convertedAmount * 100) / 100, // Round to 2 decimal places
      rate,
    }
  }

  /**
   * Convert amount to KWD (the base currency for the app)
   */
  static async convertToKWD(amount: number, fromCurrency: string): Promise<{ kwdAmount: number; rate: number }> {
    if (fromCurrency === 'KWD') {
      return { kwdAmount: amount, rate: 1 }
    }

    const { convertedAmount, rate } = await this.convert(amount, fromCurrency, 'KWD')
    return { kwdAmount: convertedAmount, rate }
  }

  /**
   * Convert amount from KWD to another currency
   */
  static async convertFromKWD(kwdAmount: number, toCurrency: string): Promise<{ convertedAmount: number; rate: number }> {
    if (toCurrency === 'KWD') {
      return { convertedAmount: kwdAmount, rate: 1 }
    }

    const { convertedAmount, rate } = await this.convert(kwdAmount, 'KWD', toCurrency)
    return { convertedAmount, rate }
  }

  /**
   * Format currency amount with proper symbol and formatting
   */
  static formatCurrency(amount: number, currency: string): string {
    const symbols = {
      KWD: 'KWD',
      USD: '$',
      GBP: '£',
    }

    const symbol = symbols[currency as keyof typeof symbols] || currency
    const formattedAmount = new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(Math.abs(amount))

    return `${symbol} ${formattedAmount}`
  }

  /**
   * Update exchange rate in database
   */
  static async updateExchangeRate(
    fromCurrency: string,
    toCurrency: string,
    rate: number,
    source?: string
  ): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0]
      
      await db.exchangeRates.create({
        from_currency: fromCurrency,
        to_currency: toCurrency,
        rate,
        date: today,
        source,
      })

      // Update cache
      const cacheKey = `${fromCurrency}-${toCurrency}`
      this.rateCache.set(cacheKey, { rate, timestamp: Date.now() })
    } catch (error) {
      console.error('Failed to update exchange rate:', error)
      throw error
    }
  }

  /**
   * Fetch latest rates from external API
   */
  static async fetchLatestRates(): Promise<void> {
    const apiKey = process.env.NEXT_PUBLIC_EXCHANGE_RATE_API_KEY
    const apiUrl = process.env.NEXT_PUBLIC_EXCHANGE_RATE_API_URL || 'https://api.exchangerate-api.com/v4/latest'
    
    try {
      // Fetch rates for supported currencies
      const supportedCurrencies = this.getSupportedCurrencies()
      const today = new Date().toISOString().split('T')[0]
      
      for (const baseCurrency of supportedCurrencies) {
        try {
          let url = `${apiUrl}/${baseCurrency}`
          if (apiKey) {
            url += `?access_key=${apiKey}`
          }
          
          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'Kuwait-Subscription-Tracker/1.0',
            },
          })
          
          if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`)
          }
          
          const data = await response.json()
          
          if (data.error) {
            throw new Error(`API error: ${data.error}`)
          }
          
          // Update rates in database
          for (const targetCurrency of supportedCurrencies) {
            if (baseCurrency !== targetCurrency && data.rates?.[targetCurrency]) {
              await this.updateExchangeRate(
                baseCurrency,
                targetCurrency,
                data.rates[targetCurrency],
                'exchangerate-api'
              )
            }
          }
          
          console.log(`✅ Updated exchange rates for ${baseCurrency}`)
          
          // Add small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 100))
          
        } catch (error) {
          console.warn(`Failed to fetch rates for ${baseCurrency}:`, error)
        }
      }
      
      console.log('🎉 Exchange rate update completed')
      
    } catch (error) {
      console.error('Failed to fetch exchange rates from API:', error)
      
      // Fallback: Update with default rates
      console.log('📋 Falling back to default rates...')
      await this.updateDefaultRates()
    }
  }
  
  /**
   * Update default rates as fallback
   */
  static async updateDefaultRates(): Promise<void> {
    const today = new Date().toISOString().split('T')[0]
    
    try {
      for (const [pair, rate] of Object.entries(DEFAULT_RATES)) {
        const [from, to] = pair.split('-')
        await this.updateExchangeRate(from, to, rate, 'default')
      }
      console.log('✅ Default rates updated successfully')
    } catch (error) {
      console.error('Failed to update default rates:', error)
      throw error
    }
  }
  
  /**
   * Fetch specific currency pair rate
   */
  static async fetchCurrencyPairRate(
    fromCurrency: string, 
    toCurrency: string
  ): Promise<number | null> {
    const apiKey = process.env.NEXT_PUBLIC_EXCHANGE_RATE_API_KEY
    const apiUrl = process.env.NEXT_PUBLIC_EXCHANGE_RATE_API_URL || 'https://api.exchangerate-api.com/v4/latest'
    
    try {
      let url = `${apiUrl}/${fromCurrency}`
      if (apiKey) {
        url += `?access_key=${apiKey}`
      }
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Kuwait-Subscription-Tracker/1.0',
        },
      })
      
      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.error) {
        throw new Error(`API error: ${data.error}`)
      }
      
      return data.rates?.[toCurrency] || null
      
    } catch (error) {
      console.warn(`Failed to fetch ${fromCurrency}-${toCurrency} rate:`, error)
      return null
    }
  }

  /**
   * Clear rate cache
   */
  static clearCache(): void {
    this.rateCache.clear()
  }

  /**
   * Get supported currencies
   */
  static getSupportedCurrencies(): string[] {
    return ['KWD', 'USD', 'GBP']
  }

  /**
   * Validate currency code
   */
  static isValidCurrency(currency: string): boolean {
    return this.getSupportedCurrencies().includes(currency)
  }
}

export default CurrencyConverter
