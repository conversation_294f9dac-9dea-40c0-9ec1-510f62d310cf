// Common utility functions used across the application

import { format, differenceInDays, addDays, addMonths, addYears } from "date-fns"
import { Currency, BillingCycle, SubscriptionStatus, ReimbursementStatus } from "@/types"

// Currency formatting utilities
export const formatCurrency = (
  amount: number, 
  currency: Currency = "KWD", 
  showSymbol: boolean = true
): string => {
  const symbols = {
    USD: "$",
    GBP: "£", 
    KWD: "KWD"
  }
  
  const formatted = amount.toFixed(currency === "KWD" ? 3 : 2)
  
  if (!showSymbol) return formatted
  
  if (currency === "KWD") {
    return `${formatted} ${symbols[currency]}`
  }
  
  return `${symbols[currency]}${formatted}`
}

// Date formatting utilities
export const formatDate = (dateString: string, formatStr: string = "MMM dd, yyyy"): string => {
  try {
    return format(new Date(dateString), formatStr)
  } catch (error) {
    console.error("Error formatting date:", error)
    return "Invalid date"
  }
}

export const formatRelativeTime = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    const now = new Date()
    const diffInDays = differenceInDays(now, date)
    
    if (diffInDays === 0) return "Today"
    if (diffInDays === 1) return "Yesterday"
    if (diffInDays < 7) return `${diffInDays} days ago`
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`
    
    return `${Math.floor(diffInDays / 365)} years ago`
  } catch (error) {
    console.error("Error formatting relative time:", error)
    return "Unknown"
  }
}

// Payment due date calculations
export const getDaysUntilPayment = (nextPaymentDate: string): number => {
  try {
    return differenceInDays(new Date(nextPaymentDate), new Date())
  } catch (error) {
    console.error("Error calculating days until payment:", error)
    return 0
  }
}

export const getNextPaymentDate = (
  currentDate: string, 
  billingCycle: BillingCycle
): string => {
  try {
    const date = new Date(currentDate)
    
    switch (billingCycle) {
      case "monthly":
        return addMonths(date, 1).toISOString()
      case "quarterly":
        return addMonths(date, 3).toISOString()
      case "yearly":
        return addYears(date, 1).toISOString()
      default:
        return addMonths(date, 1).toISOString()
    }
  } catch (error) {
    console.error("Error calculating next payment date:", error)
    return new Date().toISOString()
  }
}

// Status badge utilities
export const getStatusColor = (status: SubscriptionStatus | ReimbursementStatus): string => {
  const colors = {
    active: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
    cancelled: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
    paused: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
    pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
    approved: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
    rejected: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
  }
  
  return colors[status] || "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
}

export const getPaymentUrgencyColor = (daysUntil: number): string => {
  if (daysUntil < 0) return "text-red-600 dark:text-red-400"
  if (daysUntil <= 3) return "text-orange-600 dark:text-orange-400"
  if (daysUntil <= 7) return "text-yellow-600 dark:text-yellow-400"
  return "text-green-600 dark:text-green-400"
}

// Billing cycle utilities
export const getBillingCycleMultiplier = (billingCycle: BillingCycle): number => {
  switch (billingCycle) {
    case "monthly":
      return 1
    case "quarterly":
      return 3
    case "yearly":
      return 12
    default:
      return 1
  }
}

export const convertToMonthlyAmount = (amount: number, billingCycle: BillingCycle): number => {
  const multiplier = getBillingCycleMultiplier(billingCycle)
  return amount / multiplier
}

// ID generation utilities
export const generateId = (prefix: string = ""): string => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return prefix ? `${prefix}_${timestamp}_${random}` : `${timestamp}_${random}`
}

// Validation utilities
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ""))
}

export const isValidAmount = (amount: string): boolean => {
  const num = parseFloat(amount)
  return !isNaN(num) && num > 0
}

// Search and filter utilities
export const searchItems = <T>(
  items: T[], 
  searchTerm: string, 
  searchFields: (keyof T)[]
): T[] => {
  if (!searchTerm.trim()) return items
  
  const term = searchTerm.toLowerCase()
  
  return items.filter(item => 
    searchFields.some(field => {
      const value = item[field]
      return typeof value === 'string' && value.toLowerCase().includes(term)
    })
  )
}

export const sortItems = <T>(
  items: T[], 
  sortField: keyof T, 
  sortDirection: 'asc' | 'desc' = 'asc'
): T[] => {
  return [...items].sort((a, b) => {
    const aValue = a[sortField]
    const bValue = b[sortField]
    
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
    return 0
  })
}

// File utilities
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf(".") - 1 >>> 0) + 2)
}

export const isImageFile = (filename: string): boolean => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']
  const extension = getFileExtension(filename).toLowerCase()
  return imageExtensions.includes(extension)
}

// Export utilities
export const downloadFile = (content: string, filename: string, mimeType: string = 'text/plain'): void => {
  const blob = new Blob([content], { type: mimeType })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  a.click()
  window.URL.revokeObjectURL(url)
}

export const exportToCSV = (data: any[], filename: string): void => {
  if (data.length === 0) return
  
  const headers = Object.keys(data[0])
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header]
        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value
      }).join(',')
    )
  ].join('\n')
  
  downloadFile(csvContent, filename, 'text/csv')
}

// Theme utilities
export const getThemeClass = (isDark: boolean): string => {
  return isDark ? 'dark' : 'light'
}

// Error handling utilities
export const handleError = (error: unknown, context: string = 'Unknown'): string => {
  console.error(`Error in ${context}:`, error)
  
  if (error instanceof Error) {
    return error.message
  }
  
  if (typeof error === 'string') {
    return error
  }
  
  return 'An unexpected error occurred'
}

// Debounce utility
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
