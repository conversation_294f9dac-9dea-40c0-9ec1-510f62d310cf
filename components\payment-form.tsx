"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { CalendarIcon, Upload, X } from "lucide-react"
import { format } from "date-fns"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useCurrencyRates } from "@/hooks/useCurrencyRates"
import { cn } from "@/lib/utils"
import { PaymentFormData, PaymentRecord } from "@/types"

export function PaymentForm() {
  const { rates, isLoading: ratesLoading } = useCurrencyRates()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState<PaymentFormData>({
    subscriptionName: "",
    amount: "",
    currency: "USD",
    paymentDate: new Date(),
    description: "",
    receipt: null,
    autoCreateReimbursement: false,
  })

  const [kwdAmount, setKwdAmount] = useState<number>(0)
  const [currentRate, setCurrentRate] = useState<number>(0)

  // Update KWD conversion when amount or currency changes
  useEffect(() => {
    const amount = Number.parseFloat(formData.amount)
    if (!isNaN(amount) && amount > 0) {
      const rate = formData.currency === "USD" ? rates.usdToKwd : rates.gbpToKwd
      setCurrentRate(rate)
      setKwdAmount(amount * rate)
    } else {
      setKwdAmount(0)
      setCurrentRate(formData.currency === "USD" ? rates.usdToKwd : rates.gbpToKwd)
    }
  }, [formData.amount, formData.currency, rates.usdToKwd, rates.gbpToKwd])

  const handleInputChange = (field: keyof PaymentFormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null
    handleInputChange("receipt", file)
  }

  const removeFile = () => {
    handleInputChange("receipt", null)
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    setIsSubmitting(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))

      const paymentRecord: PaymentRecord = {
        id: `payment_${Date.now()}`,
        subscriptionName: formData.subscriptionName,
        originalAmount: Number.parseFloat(formData.amount),
        originalCurrency: formData.currency,
        kwdAmount: kwdAmount,
        exchangeRate: currentRate,
        conversionTimestamp: new Date().toISOString(),
        paymentDate: formData.paymentDate.toISOString(),
        description: formData.description,
        receiptUrl: formData.receipt ? `receipt_${Date.now()}.pdf` : undefined,
        autoCreateReimbursement: formData.autoCreateReimbursement,
        createdAt: new Date().toISOString(),
      }

      // Store in localStorage for demo purposes
      const existingPayments = JSON.parse(localStorage.getItem("payments") || "[]")
      existingPayments.unshift(paymentRecord)
      localStorage.setItem("payments", JSON.stringify(existingPayments))

      // Reset form
      setFormData({
        subscriptionName: "",
        amount: "",
        currency: "USD",
        paymentDate: new Date(),
        description: "",
        receipt: null,
        autoCreateReimbursement: false,
      })

      alert("Payment recorded successfully!")
    } catch (error) {
      alert("Failed to record payment. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const isFormValid = formData.subscriptionName && formData.amount && !isNaN(Number.parseFloat(formData.amount))

  return (
    <Card className="w-full max-w-2xl dark:bg-[#28282B] dark:border-[#3a3a3d]">
      <CardHeader>
        <CardTitle>Record New Payment</CardTitle>
        <CardDescription>Add a new subscription payment with automatic KWD conversion</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="subscription">Subscription Name</Label>
              <Input
                id="subscription"
                placeholder="e.g., Netflix, Spotify, Adobe"
                value={formData.subscriptionName}
                onChange={(e) => handleInputChange("subscriptionName", e.target.value)}
                required
                className="dark:bg-[#28282B] dark:border-[#3a3a3d]"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="payment-date">Payment Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal dark:bg-[#28282B] dark:border-[#3a3a3d] dark:hover:bg-[#3a3a3d]",
                      !formData.paymentDate && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.paymentDate ? format(formData.paymentDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 dark:bg-[#28282B] dark:border-[#3a3a3d]">
                  <Calendar
                    mode="single"
                    selected={formData.paymentDate}
                    onSelect={(date) => date && handleInputChange("paymentDate", date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Amount</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                placeholder="0.00"
                value={formData.amount}
                onChange={(e) => handleInputChange("amount", e.target.value)}
                required
                className="dark:bg-[#28282B] dark:border-[#3a3a3d]"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select
                value={formData.currency}
                onValueChange={(value: "USD" | "GBP") => handleInputChange("currency", value)}
              >
                <SelectTrigger className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="dark:bg-[#28282B] dark:border-[#3a3a3d]">
                  <SelectItem value="USD" className="dark:hover:bg-[#3a3a3d]">
                    USD ($)
                  </SelectItem>
                  <SelectItem value="GBP" className="dark:hover:bg-[#3a3a3d]">
                    GBP (£)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* KWD Conversion Display */}
          {formData.amount && !isNaN(Number.parseFloat(formData.amount)) && (
            <Card className="bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-700/50">
              <CardContent className="pt-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Converted Amount:</span>
                    <span className="text-lg font-bold text-blue-700 dark:text-blue-400">
                      {kwdAmount.toFixed(3)} KWD
                    </span>
                  </div>
                  <div className="flex justify-between items-center text-sm text-muted-foreground">
                    <span>Exchange Rate:</span>
                    <span>
                      1 {formData.currency} ={" "}
                      {(formData.currency === "USD" ? rates.usdToKwd : rates.gbpToKwd).toFixed(3)} KWD
                    </span>
                  </div>
                  <div className="flex justify-between items-center text-sm text-muted-foreground">
                    <span>Rate Updated:</span>
                    <span>{format(new Date(rates.lastUpdated), "HH:mm")}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              placeholder="Additional notes about this payment..."
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              rows={3}
              className="dark:bg-[#28282B] dark:border-[#3a3a3d]"
            />
          </div>

          {/* Receipt Upload */}
          <div className="space-y-2">
            <Label htmlFor="receipt">Receipt Upload (Optional)</Label>
            {!formData.receipt ? (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center dark:border-[#3a3a3d] dark:bg-[#28282B]/50">
                <Upload className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
                <div className="mt-4">
                  <Label htmlFor="receipt-upload" className="cursor-pointer">
                    <span className="text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400">
                      Upload a receipt
                    </span>
                    <Input
                      id="receipt-upload"
                      type="file"
                      accept=".pdf,.jpg,.jpeg,.png"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                  </Label>
                  <p className="text-xs text-gray-500 mt-1">PDF, JPG, PNG up to 10MB</p>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border dark:bg-[#3a3a3d] dark:border-[#3a3a3d]">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center dark:bg-blue-900/30">
                    <Upload className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">{formData.receipt.name}</p>
                    <p className="text-xs text-gray-500">{(formData.receipt.size / 1024 / 1024).toFixed(2)} MB</p>
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={removeFile}
                  className="text-red-600 hover:text-red-700 dark:hover:bg-[#28282B]"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>

          {/* Auto-create Reimbursement */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="auto-reimburse"
              checked={formData.autoCreateReimbursement}
              onCheckedChange={(checked) => handleInputChange("autoCreateReimbursement", checked)}
            />
            <Label htmlFor="auto-reimburse" className="text-sm">
              Auto-create reimbursement request for this payment
            </Label>
          </div>

          <div className="flex gap-4">
            <Button
              type="submit"
              disabled={!isFormValid || isSubmitting || ratesLoading}
              className="flex-1 dark:bg-blue-600 dark:hover:bg-blue-700"
            >
              {isSubmitting ? "Recording Payment..." : "Record Payment"}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setFormData({
                  subscriptionName: "",
                  amount: "",
                  currency: "USD",
                  paymentDate: new Date(),
                  description: "",
                  receipt: null,
                  autoCreateReimbursement: false,
                })
              }}
              className="dark:bg-[#28282B] dark:border-[#3a3a3d] dark:hover:bg-[#3a3a3d]"
            >
              Clear
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
