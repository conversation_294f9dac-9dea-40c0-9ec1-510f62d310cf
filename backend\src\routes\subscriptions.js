const express = require('express');
const { z } = require('zod');
const { as<PERSON><PERSON><PERSON><PERSON>, ValidationError, NotFoundError, ForbiddenError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const createSubscriptionSchema = z.object({
  business_id: z.string().uuid('Invalid business ID'),
  name: z.string().min(1, 'Subscription name is required').max(255, 'Name too long'),
  description: z.string().optional(),
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().length(3, 'Currency must be 3 characters').default('KWD'),
  billing_cycle: z.enum(['monthly', 'quarterly', 'yearly'], {
    errorMap: () => ({ message: 'Billing cycle must be monthly, quarterly, or yearly' })
  }),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date must be in YYYY-MM-DD format'),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date must be in YYYY-MM-DD format').optional(),
  auto_renew: z.boolean().default(true),
  provider: z.string().optional(),
  category: z.string().optional(),
  payment_method: z.string().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional().default([])
});

const updateSubscriptionSchema = z.object({
  name: z.string().min(1, 'Subscription name is required').max(255, 'Name too long').optional(),
  description: z.string().optional(),
  amount: z.number().positive('Amount must be positive').optional(),
  currency: z.string().length(3, 'Currency must be 3 characters').optional(),
  billing_cycle: z.enum(['monthly', 'quarterly', 'yearly']).optional(),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date must be in YYYY-MM-DD format').optional(),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date must be in YYYY-MM-DD format').optional(),
  auto_renew: z.boolean().optional(),
  provider: z.string().optional(),
  category: z.string().optional(),
  payment_method: z.string().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['active', 'paused', 'cancelled', 'expired']).optional()
});

/**
 * Middleware to verify business ownership
 */
const verifyBusinessOwnership = asyncHandler(async (req, res, next) => {
  const businessId = req.body.business_id || req.query.business_id;
  
  if (!businessId) {
    throw new ValidationError('Business ID is required');
  }

  const { data: business, error } = await req.supabase
    .from('businesses')
    .select('id, owner_id, name')
    .eq('id', businessId)
    .single();

  if (error || !business) {
    throw new NotFoundError('Business not found');
  }

  if (business.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this business');
  }

  req.business = business;
  next();
});

/**
 * Middleware to verify subscription ownership
 */
const verifySubscriptionOwnership = asyncHandler(async (req, res, next) => {
  const subscriptionId = req.params.id || req.params.subscriptionId;
  
  if (!subscriptionId) {
    throw new ValidationError('Subscription ID is required');
  }

  const { data: subscription, error } = await req.supabase
    .from('subscriptions')
    .select(`
      id, 
      business_id, 
      name,
      businesses!inner(id, owner_id, name)
    `)
    .eq('id', subscriptionId)
    .single();

  if (error || !subscription) {
    throw new NotFoundError('Subscription not found');
  }

  if (subscription.businesses.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this subscription');
  }

  req.subscription = subscription;
  next();
});

/**
 * @route POST /api/subscriptions
 * @desc Create a new subscription
 * @access Private
 */
router.post('/', verifyBusinessOwnership, asyncHandler(async (req, res) => {
  const validatedData = createSubscriptionSchema.parse(req.body);
  
  // Validate date logic
  const startDate = new Date(validatedData.start_date);
  const endDate = validatedData.end_date ? new Date(validatedData.end_date) : null;
  
  if (endDate && endDate <= startDate) {
    throw new ValidationError('End date must be after start date');
  }

  const subscriptionData = {
    business_id: validatedData.business_id,
    name: validatedData.name,
    description: validatedData.description,
    amount: validatedData.amount,
    currency: validatedData.currency,
    billing_cycle: validatedData.billing_cycle,
    start_date: validatedData.start_date,
    end_date: validatedData.end_date,
    auto_renew: validatedData.auto_renew,
    provider: validatedData.provider,
    category: validatedData.category,
    payment_method: validatedData.payment_method,
    notes: validatedData.notes,
    tags: validatedData.tags,
    status: 'active'
  };

  const { data: subscription, error } = await req.supabase
    .from('subscriptions')
    .insert(subscriptionData)
    .select()
    .single();

  if (error) {
    logger.error('Failed to create subscription', { 
      userId: req.user.id, 
      businessId: validatedData.business_id,
      error: error.message,
      subscriptionData: { name: validatedData.name }
    });
    throw new ValidationError('Failed to create subscription');
  }

  logger.info('Subscription created successfully', { 
    userId: req.user.id,
    businessId: validatedData.business_id,
    subscriptionId: subscription.id,
    name: subscription.name
  });

  res.status(201).json({
    message: 'Subscription created successfully',
    subscription: {
      id: subscription.id,
      businessId: subscription.business_id,
      name: subscription.name,
      description: subscription.description,
      amount: subscription.amount,
      currency: subscription.currency,
      billingCycle: subscription.billing_cycle,
      startDate: subscription.start_date,
      endDate: subscription.end_date,
      autoRenew: subscription.auto_renew,
      provider: subscription.provider,
      category: subscription.category,
      paymentMethod: subscription.payment_method,
      notes: subscription.notes,
      tags: subscription.tags,
      status: subscription.status,
      createdAt: subscription.created_at,
      updatedAt: subscription.updated_at
    }
  });
}));

/**
 * @route GET /api/subscriptions
 * @desc Get all subscriptions for a business
 * @access Private
 */
router.get('/', asyncHandler(async (req, res) => {
  const { 
    business_id, 
    page = 1, 
    limit = 10, 
    search, 
    status, 
    category, 
    billing_cycle,
    sort_by = 'created_at',
    sort_order = 'desc'
  } = req.query;

  if (!business_id) {
    throw new ValidationError('Business ID is required');
  }

  // Verify business ownership
  const { data: business, error: businessError } = await req.supabase
    .from('businesses')
    .select('id, owner_id')
    .eq('id', business_id)
    .single();

  if (businessError || !business) {
    throw new NotFoundError('Business not found');
  }

  if (business.owner_id !== req.user.id) {
    throw new ForbiddenError('You do not have permission to access this business');
  }

  const offset = (page - 1) * limit;
  const validSortColumns = ['name', 'amount', 'start_date', 'created_at', 'status'];
  const sortColumn = validSortColumns.includes(sort_by) ? sort_by : 'created_at';
  const sortDirection = sort_order === 'asc' ? true : false;

  let query = req.supabase
    .from('subscriptions')
    .select('*', { count: 'exact' })
    .eq('business_id', business_id)
    .order(sortColumn, { ascending: sortDirection })
    .range(offset, offset + limit - 1);

  // Apply filters
  if (search) {
    query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,provider.ilike.%${search}%`);
  }

  if (status) {
    query = query.eq('status', status);
  }

  if (category) {
    query = query.eq('category', category);
  }

  if (billing_cycle) {
    query = query.eq('billing_cycle', billing_cycle);
  }

  const { data: subscriptions, error, count } = await query;

  if (error) {
    logger.error('Failed to fetch subscriptions', { 
      userId: req.user.id, 
      businessId: business_id,
      error: error.message 
    });
    throw new ValidationError('Failed to fetch subscriptions');
  }

  const totalPages = Math.ceil(count / limit);

  res.json({
    subscriptions: subscriptions.map(sub => ({
      id: sub.id,
      businessId: sub.business_id,
      name: sub.name,
      description: sub.description,
      amount: sub.amount,
      currency: sub.currency,
      billingCycle: sub.billing_cycle,
      startDate: sub.start_date,
      endDate: sub.end_date,
      autoRenew: sub.auto_renew,
      provider: sub.provider,
      category: sub.category,
      paymentMethod: sub.payment_method,
      notes: sub.notes,
      tags: sub.tags,
      status: sub.status,
      createdAt: sub.created_at,
      updatedAt: sub.updated_at
    })),
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: count,
      itemsPerPage: parseInt(limit),
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    }
  });
}));

/**
 * @route GET /api/subscriptions/:id
 * @desc Get a specific subscription
 * @access Private
 */
router.get('/:id', verifySubscriptionOwnership, asyncHandler(async (req, res) => {
  const { data: subscription, error } = await req.supabase
    .from('subscriptions')
    .select('*')
    .eq('id', req.params.id)
    .single();

  if (error) {
    logger.error('Failed to fetch subscription', { 
      userId: req.user.id, 
      subscriptionId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to fetch subscription');
  }

  res.json({
    subscription: {
      id: subscription.id,
      businessId: subscription.business_id,
      name: subscription.name,
      description: subscription.description,
      amount: subscription.amount,
      currency: subscription.currency,
      billingCycle: subscription.billing_cycle,
      startDate: subscription.start_date,
      endDate: subscription.end_date,
      autoRenew: subscription.auto_renew,
      provider: subscription.provider,
      category: subscription.category,
      paymentMethod: subscription.payment_method,
      notes: subscription.notes,
      tags: subscription.tags,
      status: subscription.status,
      createdAt: subscription.created_at,
      updatedAt: subscription.updated_at
    }
  });
}));

/**
 * @route PUT /api/subscriptions/:id
 * @desc Update a subscription
 * @access Private
 */
router.put('/:id', verifySubscriptionOwnership, asyncHandler(async (req, res) => {
  const validatedData = updateSubscriptionSchema.parse(req.body);
  
  // Remove undefined values
  const updateData = Object.fromEntries(
    Object.entries(validatedData).filter(([_, value]) => value !== undefined)
  );

  if (Object.keys(updateData).length === 0) {
    throw new ValidationError('No valid fields to update');
  }

  // Validate date logic if both dates are being updated
  if (updateData.start_date && updateData.end_date) {
    const startDate = new Date(updateData.start_date);
    const endDate = new Date(updateData.end_date);
    
    if (endDate <= startDate) {
      throw new ValidationError('End date must be after start date');
    }
  }

  const { data: subscription, error } = await req.supabase
    .from('subscriptions')
    .update(updateData)
    .eq('id', req.params.id)
    .select()
    .single();

  if (error) {
    logger.error('Failed to update subscription', { 
      userId: req.user.id, 
      subscriptionId: req.params.id,
      error: error.message,
      updateData
    });
    throw new ValidationError('Failed to update subscription');
  }

  logger.info('Subscription updated successfully', { 
    userId: req.user.id,
    subscriptionId: subscription.id,
    name: subscription.name,
    updatedFields: Object.keys(updateData)
  });

  res.json({
    message: 'Subscription updated successfully',
    subscription: {
      id: subscription.id,
      businessId: subscription.business_id,
      name: subscription.name,
      description: subscription.description,
      amount: subscription.amount,
      currency: subscription.currency,
      billingCycle: subscription.billing_cycle,
      startDate: subscription.start_date,
      endDate: subscription.end_date,
      autoRenew: subscription.auto_renew,
      provider: subscription.provider,
      category: subscription.category,
      paymentMethod: subscription.payment_method,
      notes: subscription.notes,
      tags: subscription.tags,
      status: subscription.status,
      createdAt: subscription.created_at,
      updatedAt: subscription.updated_at
    }
  });
}));

/**
 * @route DELETE /api/subscriptions/:id
 * @desc Delete a subscription
 * @access Private
 */
router.delete('/:id', verifySubscriptionOwnership, asyncHandler(async (req, res) => {
  // Check if subscription has associated payments
  const { data: payments } = await req.supabase
    .from('payments')
    .select('id')
    .eq('subscription_id', req.params.id)
    .limit(1);

  if (payments?.length > 0) {
    throw new ValidationError(
      'Cannot delete subscription with existing payments. Please delete all associated payments first.'
    );
  }

  const { error } = await req.supabase
    .from('subscriptions')
    .delete()
    .eq('id', req.params.id);

  if (error) {
    logger.error('Failed to delete subscription', { 
      userId: req.user.id, 
      subscriptionId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to delete subscription');
  }

  logger.info('Subscription deleted successfully', { 
    userId: req.user.id,
    subscriptionId: req.params.id,
    name: req.subscription.name
  });

  res.json({
    message: 'Subscription deleted successfully'
  });
}));

/**
 * @route POST /api/subscriptions/:id/pause
 * @desc Pause a subscription
 * @access Private
 */
router.post('/:id/pause', verifySubscriptionOwnership, asyncHandler(async (req, res) => {
  const { data: subscription, error } = await req.supabase
    .from('subscriptions')
    .update({ status: 'paused' })
    .eq('id', req.params.id)
    .select()
    .single();

  if (error) {
    logger.error('Failed to pause subscription', { 
      userId: req.user.id, 
      subscriptionId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to pause subscription');
  }

  logger.info('Subscription paused successfully', { 
    userId: req.user.id,
    subscriptionId: subscription.id,
    name: subscription.name
  });

  res.json({
    message: 'Subscription paused successfully',
    subscription: {
      id: subscription.id,
      status: subscription.status
    }
  });
}));

/**
 * @route POST /api/subscriptions/:id/resume
 * @desc Resume a paused subscription
 * @access Private
 */
router.post('/:id/resume', verifySubscriptionOwnership, asyncHandler(async (req, res) => {
  const { data: subscription, error } = await req.supabase
    .from('subscriptions')
    .update({ status: 'active' })
    .eq('id', req.params.id)
    .select()
    .single();

  if (error) {
    logger.error('Failed to resume subscription', { 
      userId: req.user.id, 
      subscriptionId: req.params.id,
      error: error.message 
    });
    throw new ValidationError('Failed to resume subscription');
  }

  logger.info('Subscription resumed successfully', { 
    userId: req.user.id,
    subscriptionId: subscription.id,
    name: subscription.name
  });

  res.json({
    message: 'Subscription resumed successfully',
    subscription: {
      id: subscription.id,
      status: subscription.status
    }
  });
}));

module.exports = router;