"use client"

import { useState, useEffect, useCallback } from "react"
import { CurrencyRates, CurrencyApiResponse, UseCurrencyRatesReturn } from "@/types"
import { Storage } from "@/lib/storage"

const CACHE_DURATION = 30 * 60 * 1000 // 30 minutes in milliseconds
const POLL_INTERVAL = 30 * 60 * 1000 // 30 minutes

export function useCurrencyRates(): UseCurrencyRatesReturn {
  const [rates, setRates] = useState<CurrencyRates>({
    usdToKwd: 0.307, // fallback rates
    gbpToKwd: 0.385,
    lastUpdated: new Date().toISOString(),
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isOnline, setIsOnline] = useState(true)

  // Load cached rates on mount
  useEffect(() => {
    loadCachedRates()
  }, [])

  const loadCachedRates = async () => {
    try {
      const cached = await Storage.currencyRates.get()
      if (cached) {
        const cacheAge = Date.now() - new Date(cached.timestamp).getTime()

        if (cacheAge < CACHE_DURATION) {
          setRates(cached.rates)
          return
        }
      }

      // If no valid cache, fetch immediately
      fetchRates()
    } catch (error) {
      console.error('Error loading cached rates:', error)
      fetchRates()
    }
  }

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
    }
  }, [])

  const fetchRates = useCallback(async () => {
    if (!isOnline) {
      setError("Offline - using cached rates")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Try multiple API endpoints for better reliability
      let newRates: CurrencyRates | null = null

      // First try: Exchange Rate API (free tier)
      try {
        const response = await fetch("https://api.exchangerate-api.com/v4/latest/USD")
        if (response.ok) {
          const data = await response.json()
          if (data.rates && data.rates.KWD) {
            // Calculate GBP to KWD via USD
            const usdToKwd = data.rates.KWD
            const gbpToUsd = data.rates.GBP ? (1 / data.rates.GBP) : 1.27 // fallback
            const gbpToKwd = usdToKwd * gbpToUsd

            newRates = {
              usdToKwd: usdToKwd,
              gbpToKwd: gbpToKwd,
              lastUpdated: new Date().toISOString(),
            }
          }
        }
      } catch (apiError) {
        console.warn("Primary API failed, trying fallback:", apiError)
      }

      // If primary API failed, use reliable static rates
      if (!newRates) {
        console.warn("Using static fallback rates - primary API unavailable")

        newRates = {
          usdToKwd: 0.307, // Current approximate rate
          gbpToKwd: 0.385, // Current approximate rate
          lastUpdated: new Date().toISOString(),
        }
        setError("Using static rates - API unavailable")
      }

      // Update rates and cache them
      setRates(newRates)
      Storage.currencyRates.set(newRates, new Date().toISOString())

      // Clear error if we successfully got live rates
      if (newRates && !error?.includes("static")) {
        setError(null)
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch rates"
      setError("Failed to fetch rates - using cached/fallback")
      console.error("Currency rate fetch error:", err)
    } finally {
      setIsLoading(false)
    }
  }, [isOnline, error])

  // Set up polling
  useEffect(() => {
    const interval = setInterval(fetchRates, POLL_INTERVAL)
    return () => clearInterval(interval)
  }, [fetchRates])

  const refreshRates = useCallback(() => {
    fetchRates()
  }, [fetchRates])

  return {
    rates,
    isLoading,
    error,
    isOnline,
    refreshRates,
  }
}
