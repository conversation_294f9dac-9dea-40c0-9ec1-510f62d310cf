"use client"

import type React from "react"
import {
  CreditCard,
  DollarSign,
  FileText,
  Home,
  LogOut,
  Receipt,
  RefreshCw,
  Settings,
  User,
  Wallet,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar"
import { useCurrencyRates } from "@/hooks/useCurrencyRates"
import { ThemeToggle } from "@/components/theme-toggle"
import { SharedLayoutProps } from "@/types"
import { useAuth } from "@/contexts/auth-context"

const navigationItems = [
  { title: "Dashboard", url: "/", icon: Home },
  { title: "Subscriptions", url: "/subscriptions", icon: CreditCard },
  { title: "Payments", url: "/payments", icon: Wallet },
  { title: "Reimbursements", url: "/reimbursements", icon: Receipt },
  { title: "Reports", url: "/reports", icon: FileText },
]

function CurrencyTicker() {
  const { rates, isLoading, error, isOnline, refreshRates } = useCurrencyRates()

  const formatTime = (isoString: string) => {
    return new Date(isoString).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  return (
    <div className="bg-muted/50 border-b px-4 py-2 dark:bg-[#28282B]/80 dark:border-[#3a3a3d]">
      <div className="flex items-center justify-between">
        <div className="flex items-center justify-center gap-6 text-sm flex-1">
          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-700/50"
            >
              USD → KWD
            </Badge>
            <span className="font-mono">
              <span className="font-bold">{rates.usdToKwd.toFixed(3)} KWD</span>
              <span className="text-muted-foreground text-xs ml-1">(per $1 USD)</span>
            </span>
          </div>
          <Separator orientation="vertical" className="h-4 dark:bg-[#3a3a3d]" />
          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-700/50"
            >
              GBP → KWD
            </Badge>
            <span className="font-mono">
              <span className="font-bold">{rates.gbpToKwd.toFixed(3)} KWD</span>
              <span className="text-muted-foreground text-xs ml-1">(per £1 GBP)</span>
            </span>
          </div>
        </div>

        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          {!isOnline && (
            <Badge
              variant="outline"
              className="bg-red-50 text-red-700 border-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-700/50"
            >
              Offline
            </Badge>
          )}
          {error && (
            <Badge
              variant="outline"
              className="bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-700/50"
            >
              {error.includes("Offline") ? "Cached" : "Error"}
            </Badge>
          )}
          <span>Updated: {formatTime(rates.lastUpdated)}</span>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 dark:hover:bg-[#3a3a3d]"
            onClick={refreshRates}
            disabled={isLoading || !isOnline}
          >
            <RefreshCw className={`h-3 w-3 ${isLoading ? "animate-spin" : ""}`} />
          </Button>
        </div>
      </div>
    </div>
  )
}

function SidebarToggleButton() {
  const { state, toggleSidebar } = useSidebar()
  const isCollapsed = state === "collapsed"

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        onClick={toggleSidebar}
        tooltip={`${isCollapsed ? "Expand" : "Collapse"} Sidebar (Ctrl+B)`}
        className="text-gray-900 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-200 dark:hover:bg-[#3a3a3d] dark:hover:text-white"
      >
        {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        <span className="group-data-[collapsible=icon]:hidden">{isCollapsed ? "Expand" : "Collapse"}</span>
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}

function AppSidebar({ activeUrl = "/" }: { activeUrl?: string }) {
  // Get user profile from auth context
  const { profile, signOut, loading } = useAuth()

  // Handle loading state
  if (loading) {
    return null // or a loading skeleton
  }

  const userName = profile
    ? `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || "User"
    : "User"
  const userInitials = profile
    ? `${(profile.first_name || '').charAt(0)}${(profile.last_name || '').charAt(0)}`.toUpperCase()
    : "U"

  return (
    <Sidebar
      collapsible="icon"
      className="bg-gray-900 text-white border-r border-gray-800 dark:bg-[#28282B] dark:text-white dark:border-[#3a3a3d] transition-all duration-300 ease-in-out"
    >
      <SidebarHeader className="border-b border-gray-800 dark:border-[#3a3a3d]">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size="lg"
              className="hover:bg-gray-800 dark:hover:bg-[#3a3a3d] text-black"
              onClick={() => window.location.href = "/"}
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-blue-600 text-white dark:bg-blue-500">
                <DollarSign className="size-4" />
              </div>
              <div className="flex flex-col gap-0.5 leading-none group-data-[collapsible=icon]:hidden">
                <span className="font-semibold border-black bg-transparent text-white">SubTracker</span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent className="bg-white dark:bg-[#28282B]">
        <SidebarGroup>
          <SidebarGroupLabel className="text-gray-900 dark:text-gray-300">Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => {
                const Icon = item.icon
                const isActive = activeUrl === item.url
                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      isActive={isActive}
                      tooltip={item.title}
                      className={`text-gray-900 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-200 dark:hover:bg-[#3a3a3d] dark:hover:text-white ${
                        isActive
                          ? "bg-gray-100 text-gray-900 border-l-4 border-l-blue-500 rounded-l-none dark:bg-[#3a3a3d] dark:text-white dark:border-l-blue-400"
                          : ""
                      }`}
                      onClick={() => window.location.href = item.url}
                    >
                      <Icon className="h-4 w-4" />
                      <span className="group-data-[collapsible=icon]:hidden">{item.title}</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Sidebar Toggle Section */}
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarToggleButton />
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="border-t border-gray-200 bg-white dark:border-[#3a3a3d] dark:bg-[#28282B]">
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  className="text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-[#3a3a3d]"
                  tooltip={userName}
                >
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={profile?.avatar_url || "/placeholder-user.jpg"} alt="User" />
                    <AvatarFallback className="bg-gray-200 text-gray-900 dark:bg-[#3a3a3d] dark:text-white">
                      {userInitials}
                    </AvatarFallback>
                  </Avatar>
                  <span className="group-data-[collapsible=icon]:hidden">{userName}</span>
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="top"
                className="w-[--radix-popper-anchor-width] dark:bg-[#28282B] dark:border-[#3a3a3d]"
              >
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator className="dark:bg-[#3a3a3d]" />
                <DropdownMenuItem asChild className="dark:hover:bg-[#3a3a3d]">
                  <a href="/profile" className="flex items-center w-full">
                    <User className="mr-2 h-4 w-4" />
                    Profile
                  </a>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="dark:hover:bg-[#3a3a3d]">
                  <a href="/settings" className="flex items-center w-full">
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </a>
                </DropdownMenuItem>
                <DropdownMenuSeparator className="dark:bg-[#3a3a3d]" />
                <DropdownMenuItem
                  className="dark:hover:bg-[#3a3a3d] cursor-pointer"
                  onClick={async () => {
                    try {
                      await signOut()
                      // Redirect to login page after successful logout
                      window.location.href = "/auth/login"
                    } catch (error) {
                      console.error('Logout error:', error)
                      // Fallback: redirect to login even if logout fails
                      window.location.href = "/auth/login"
                    }
                  }}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

export function SharedLayout({
  activeUrl = "/",
  children,
}: SharedLayoutProps) {
  return (
    <SidebarProvider>
      <AppSidebar activeUrl={activeUrl} />
      <SidebarInset>
        <CurrencyTicker />
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 dark:border-[#3a3a3d] dark:bg-[#28282B]">
          <SidebarTrigger className="-ml-1 dark:hover:bg-[#3a3a3d]" />
          <Separator orientation="vertical" className="mr-2 h-4 dark:bg-[#3a3a3d]" />
          <div className="flex items-center gap-2 flex-1">
            <span className="text-sm font-medium">Kuwait Subscription Tracker</span>
          </div>
          <ThemeToggle />
        </header>
        <div className="scrollbar-thin overflow-auto dark:bg-[#28282B]">{children}</div>
      </SidebarInset>
    </SidebarProvider>
  )
}
