"use client"

import { useState, useEffect } from "react"
import { CreditCard, FileText, Plus } from "lucide-react"
import { format } from "date-fns"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { PaymentForm } from "@/components/payment-form"
import { SharedLayout } from "@/components/shared-layout"
import { PaymentRecord } from "@/types"
import { Storage } from "@/lib/storage"

export function PaymentsPage() {
  const [payments, setPayments] = useState<PaymentRecord[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  useEffect(() => {
    // Load payments from localStorage using type-safe storage
    const storedPayments = Storage.payments.get()
    setPayments(storedPayments)
  }, [isDialogOpen]) // Refresh when dialog closes

  const totalKwdThisMonth = payments
    .filter((payment) => {
      const paymentDate = new Date(payment.paymentDate)
      const now = new Date()
      return paymentDate.getMonth() === now.getMonth() && paymentDate.getFullYear() === now.getFullYear()
    })
    .reduce((sum, payment) => sum + payment.kwdAmount, 0)

  return (
    <SharedLayout activeUrl="/payments">
      <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Payments</h1>
            <p className="text-muted-foreground">Track and manage your subscription payments</p>
          </div>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Record Payment
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Record New Payment</DialogTitle>
                <DialogDescription>Add a new subscription payment with automatic KWD conversion</DialogDescription>
              </DialogHeader>
              <PaymentForm />
            </DialogContent>
          </Dialog>
        </div>

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Payments</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{payments.length}</div>
              <p className="text-xs text-muted-foreground">All time</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Month (KWD)</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalKwdThisMonth.toFixed(2)} KWD</div>
              <p className="text-xs text-muted-foreground">
                {
                  payments.filter((p) => {
                    const paymentDate = new Date(p.paymentDate)
                    const now = new Date()
                    return paymentDate.getMonth() === now.getMonth() && paymentDate.getFullYear() === now.getFullYear()
                  }).length
                }{" "}
                payments
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Reimbursements</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{payments.filter((p) => p.autoCreateReimbursement).length}</div>
              <p className="text-xs text-muted-foreground">Auto-created</p>
            </CardContent>
          </Card>
        </div>

        {/* Payments List */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Payments</CardTitle>
            <CardDescription>Your subscription payment history</CardDescription>
          </CardHeader>
          <CardContent>
            {payments.length === 0 ? (
              <div className="text-center py-8">
                <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-4 text-lg font-medium">No payments recorded</h3>
                <p className="text-muted-foreground">Get started by recording your first payment</p>
              </div>
            ) : (
              <div className="space-y-4">
                {payments.map((payment) => (
                  <div
                    key={payment.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <CreditCard className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-medium">{payment.subscriptionName}</h4>
                        <p className="text-sm text-muted-foreground">
                          {format(new Date(payment.paymentDate), "MMM dd, yyyy")}
                        </p>
                      </div>
                    </div>

                    <div className="text-right space-y-1">
                      <div className="font-bold">{payment.kwdAmount.toFixed(2)} KWD</div>
                      <div className="text-sm text-muted-foreground">
                        from {payment.originalAmount.toFixed(2)} {payment.originalCurrency}
                      </div>
                      <div className="flex gap-2">
                        {payment.receiptUrl && (
                          <Badge variant="outline" className="text-xs">
                            Receipt
                          </Badge>
                        )}
                        {payment.autoCreateReimbursement && (
                          <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                            Reimbursement
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </SharedLayout>
  )
}
