"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { format } from "date-fns"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { PaymentRecord, ReimbursementRecord } from "@/types"
import { Storage } from "@/lib/storage"

export function ReimbursementForm() {
  const [payments, setPayments] = useState<PaymentRecord[]>([])
  const [selectedPayment, setSelectedPayment] = useState<string>("")
  const [notes, setNotes] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadAvailablePayments()
  }, [])

  const loadAvailablePayments = async () => {
    try {
      setLoading(true)
      const [storedPayments, existingReimbursements] = await Promise.all([
        Storage.payments.get(),
        Storage.reimbursements.get()
      ])

      const paymentsWithoutReimbursements = storedPayments.filter(
        (payment: PaymentRecord) =>
          !existingReimbursements.some((reimb: ReimbursementRecord) => reimb.linkedPaymentId === payment.id),
      )

      setPayments(paymentsWithoutReimbursements)
    } catch (error) {
      console.error('Error loading payments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    if (!selectedPayment) return

    setIsSubmitting(true)

    try {
      const payment = payments.find((p) => p.id === selectedPayment)
      if (!payment) throw new Error("Payment not found")

      const reimbursementRecord: ReimbursementRecord = {
        id: `reimb_${Date.now()}`,
        linkedPaymentId: payment.id,
        kwdAmount: payment.kwdAmount, // Always in KWD
        status: "pending",
        requestDate: new Date().toISOString(),
        notes,
        createdAt: new Date().toISOString(),
      }

      // Store reimbursement using Supabase storage
      await Storage.reimbursements.add(reimbursementRecord)

      // Reset form
      setSelectedPayment("")
      setNotes("")

      // Refresh available payments
      await loadAvailablePayments()

      alert("Reimbursement request submitted successfully!")
    } catch (error) {
      console.error('Error submitting reimbursement:', error)
      alert("Failed to submit reimbursement request. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const selectedPaymentData = payments.find((p) => p.id === selectedPayment)

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Request Reimbursement</CardTitle>
        <CardDescription>Submit a reimbursement request for a subscription payment (KWD only)</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading payments...</p>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="payment">Select Payment to Reimburse</Label>
            <Select value={selectedPayment} onValueChange={setSelectedPayment}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a payment..." />
              </SelectTrigger>
              <SelectContent>
                {payments.map((payment) => (
                  <SelectItem key={payment.id} value={payment.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{payment.subscriptionName}</span>
                      <div className="ml-4 text-right">
                        <div className="font-bold">{payment.kwdAmount.toFixed(2)} KWD</div>
                        <div className="text-xs text-muted-foreground">
                          from {payment.originalAmount.toFixed(2)} {payment.originalCurrency}
                        </div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedPaymentData && (
            <Card className="bg-green-50 border-green-200">
              <CardContent className="pt-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Reimbursement Amount:</span>
                    <span className="text-lg font-bold text-green-700">
                      {selectedPaymentData.kwdAmount.toFixed(2)} KWD
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <div className="flex justify-between">
                      <span>Original Payment:</span>
                      <span>
                        {selectedPaymentData.originalAmount.toFixed(2)} {selectedPaymentData.originalCurrency}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Exchange Rate Used:</span>
                      <span>{selectedPaymentData.exchangeRate.toFixed(3)} KWD</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Conversion Date:</span>
                      <span>{format(new Date(selectedPaymentData.conversionTimestamp), "MMM dd, yyyy HH:mm")}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Payment Date:</span>
                      <span>{format(new Date(selectedPaymentData.paymentDate), "MMM dd, yyyy")}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Additional information about this reimbursement request..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          <div className="flex gap-4">
            <Button type="submit" disabled={!selectedPayment || isSubmitting} className="flex-1">
              {isSubmitting ? "Submitting Request..." : "Submit Reimbursement Request"}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setSelectedPayment("")
                setNotes("")
              }}
            >
              Clear Form
            </Button>
          </div>
        </form>
        )}
      </CardContent>
    </Card>
  )
}
