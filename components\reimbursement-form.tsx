"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { format } from "date-fns"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { PaymentRecord, ReimbursementRecord } from "@/types"

export function ReimbursementForm() {
  const [payments, setPayments] = useState<PaymentRecord[]>([])
  const [selectedPayment, setSelectedPayment] = useState<string>("")
  const [notes, setNotes] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    // Load payments that don't have reimbursements yet
    const storedPayments = JSON.parse(localStorage.getItem("payments") || "[]")
    const existingReimbursements = JSON.parse(localStorage.getItem("reimbursements") || "[]")

    const paymentsWithoutReimbursements = storedPayments.filter(
      (payment: PaymentRecord) =>
        !existingReimbursements.some((reimb: ReimbursementRecord) => reimb.linkedPaymentId === payment.id),
    )

    setPayments(paymentsWithoutReimbursements)
  }, [])

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    if (!selectedPayment) return

    setIsSubmitting(true)

    try {
      const payment = payments.find((p) => p.id === selectedPayment)
      if (!payment) throw new Error("Payment not found")

      const reimbursementRecord: ReimbursementRecord = {
        id: `reimb_${Date.now()}`,
        linkedPaymentId: payment.id,
        kwdAmount: payment.kwdAmount, // Always in KWD
        status: "pending",
        requestDate: new Date().toISOString(),
        notes,
        createdAt: new Date().toISOString(),
      }

      // Store reimbursement
      const existingReimbursements = JSON.parse(localStorage.getItem("reimbursements") || "[]")
      existingReimbursements.unshift(reimbursementRecord)
      localStorage.setItem("reimbursements", JSON.stringify(existingReimbursements))

      // Reset form
      setSelectedPayment("")
      setNotes("")

      // Refresh available payments
      const updatedPayments = payments.filter((p) => p.id !== selectedPayment)
      setPayments(updatedPayments)

      alert("Reimbursement request submitted successfully!")
    } catch (error) {
      alert("Failed to submit reimbursement request. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const selectedPaymentData = payments.find((p) => p.id === selectedPayment)

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Request Reimbursement</CardTitle>
        <CardDescription>Submit a reimbursement request for a subscription payment (KWD only)</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="payment">Select Payment to Reimburse</Label>
            <Select value={selectedPayment} onValueChange={setSelectedPayment}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a payment..." />
              </SelectTrigger>
              <SelectContent>
                {payments.map((payment) => (
                  <SelectItem key={payment.id} value={payment.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{payment.subscriptionName}</span>
                      <div className="ml-4 text-right">
                        <div className="font-bold">{payment.kwdAmount.toFixed(2)} KWD</div>
                        <div className="text-xs text-muted-foreground">
                          from {payment.originalAmount.toFixed(2)} {payment.originalCurrency}
                        </div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedPaymentData && (
            <Card className="bg-green-50 border-green-200">
              <CardContent className="pt-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Reimbursement Amount:</span>
                    <span className="text-lg font-bold text-green-700">
                      {selectedPaymentData.kwdAmount.toFixed(2)} KWD
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <div className="flex justify-between">
                      <span>Original Payment:</span>
                      <span>
                        {selectedPaymentData.originalAmount.toFixed(2)} {selectedPaymentData.originalCurrency}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Exchange Rate Used:</span>
                      <span>{selectedPaymentData.exchangeRate.toFixed(3)} KWD</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Conversion Date:</span>
                      <span>{format(new Date(selectedPaymentData.conversionTimestamp), "MMM dd, yyyy HH:mm")}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Payment Date:</span>
                      <span>{format(new Date(selectedPaymentData.paymentDate), "MMM dd, yyyy")}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Additional information about this reimbursement request..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          <div className="flex gap-4">
            <Button type="submit" disabled={!selectedPayment || isSubmitting} className="flex-1">
              {isSubmitting ? "Submitting Request..." : "Submit Reimbursement Request"}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setSelectedPayment("")
                setNotes("")
              }}
            >
              Clear Form
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
