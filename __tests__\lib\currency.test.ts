import { CurrencyConverter } from '@/lib/currency'

// Mock the database module
jest.mock('@/lib/supabase', () => ({
  db: {
    exchangeRates: {
      getLatest: jest.fn(),
      create: jest.fn(),
    },
  },
}))

describe('CurrencyConverter', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    CurrencyConverter.clearCache()
  })

  describe('getSupportedCurrencies', () => {
    it('should return supported currencies', () => {
      const currencies = CurrencyConverter.getSupportedCurrencies()
      expect(currencies).toEqual(['KWD', 'USD', 'GBP'])
    })
  })

  describe('isValidCurrency', () => {
    it('should return true for supported currencies', () => {
      expect(CurrencyConverter.isValidCurrency('KWD')).toBe(true)
      expect(CurrencyConverter.isValidCurrency('USD')).toBe(true)
      expect(CurrencyConverter.isValidCurrency('GBP')).toBe(true)
    })

    it('should return false for unsupported currencies', () => {
      expect(CurrencyConverter.isValidCurrency('EUR')).toBe(false)
      expect(CurrencyConverter.isValidCurrency('JPY')).toBe(false)
      expect(CurrencyConverter.isValidCurrency('')).toBe(false)
    })
  })

  describe('formatCurrency', () => {
    it('should format USD correctly', () => {
      expect(CurrencyConverter.formatCurrency(100.5, 'USD')).toBe('$ 100.50')
    })

    it('should format GBP correctly', () => {
      expect(CurrencyConverter.formatCurrency(75.25, 'GBP')).toBe('£ 75.25')
    })

    it('should format KWD correctly', () => {
      expect(CurrencyConverter.formatCurrency(30.123, 'KWD')).toBe('KWD 30.12')
    })

    it('should handle negative amounts', () => {
      expect(CurrencyConverter.formatCurrency(-50.75, 'USD')).toBe('$ 50.75')
    })
  })

  describe('convert', () => {
    it('should return 1:1 for same currency conversion', async () => {
      const result = await CurrencyConverter.convert(100, 'USD', 'USD')
      expect(result).toEqual({
        convertedAmount: 100,
        rate: 1,
      })
    })
  })

  describe('convertToKWD', () => {
    it('should return same amount for KWD to KWD', async () => {
      const result = await CurrencyConverter.convertToKWD(100, 'KWD')
      expect(result).toEqual({
        kwdAmount: 100,
        rate: 1,
      })
    })
  })

  describe('convertFromKWD', () => {
    it('should return same amount for KWD to KWD', async () => {
      const result = await CurrencyConverter.convertFromKWD(100, 'KWD')
      expect(result).toEqual({
        convertedAmount: 100,
        rate: 1,
      })
    })
  })
})