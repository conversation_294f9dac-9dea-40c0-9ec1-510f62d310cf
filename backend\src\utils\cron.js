const cron = require('node-cron');
const { createClient } = require('@supabase/supabase-js');
const logger = require('./logger');

// Initialize Supabase client for cron jobs
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * Fetch exchange rates from external API
 */
async function fetchExchangeRates() {
  try {
    logger.info('Starting exchange rate update job');
    
    const supportedCurrencies = ['KWD', 'USD', 'GBP'];
    const today = new Date().toISOString().split('T')[0];
    const apiKey = process.env.EXCHANGE_RATE_API_KEY;
    const apiUrl = process.env.EXCHANGE_RATE_API_URL || 'https://api.exchangerate-api.com/v4/latest';
    
    // Check if rates for today already exist
    const { data: existingRates } = await supabase
      .from('exchange_rates')
      .select('from_currency, to_currency')
      .eq('date', today);
    
    const existingPairs = new Set(
      existingRates?.map(rate => `${rate.from_currency}-${rate.to_currency}`) || []
    );
    
    const ratesToInsert = [];
    let apiSuccess = false;
    
    // Try to fetch from real API
    for (const baseCurrency of supportedCurrencies) {
      try {
        let url = `${apiUrl}/${baseCurrency}`;
        if (apiKey) {
          url += `?access_key=${apiKey}`;
        }
        
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Kuwait-Subscription-Tracker/1.0',
          },
        });
        
        if (!response.ok) {
          throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
          throw new Error(`API error: ${data.error}`);
        }
        
        apiSuccess = true;
        
        // Add rates for supported currencies
        for (const targetCurrency of supportedCurrencies) {
          const pairKey = `${baseCurrency}-${targetCurrency}`;
          
          if (baseCurrency !== targetCurrency && data.rates?.[targetCurrency] && !existingPairs.has(pairKey)) {
            ratesToInsert.push({
              from_currency: baseCurrency,
              to_currency: targetCurrency,
              rate: data.rates[targetCurrency],
              date: today,
              source: 'exchangerate-api'
            });
          }
        }
        
        logger.info(`✅ Fetched exchange rates for ${baseCurrency}`);
        
        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        logger.warn(`Failed to fetch rates for ${baseCurrency}: ${error.message}`);
      }
    }
    
    // Fallback to default rates if API failed
    if (!apiSuccess) {
      logger.warn('API failed, using fallback rates');
      
      const defaultRates = {
        'USD-KWD': 0.31,
        'GBP-KWD': 0.38,
        'KWD-USD': 3.25,
        'KWD-GBP': 2.63,
        'USD-GBP': 0.82,
        'GBP-USD': 1.22,
      };
      
      for (const [pair, rate] of Object.entries(defaultRates)) {
        const [from, to] = pair.split('-');
        if (!existingPairs.has(pair)) {
          ratesToInsert.push({
            from_currency: from,
            to_currency: to,
            rate: rate,
            date: today,
            source: 'default'
          });
        }
      }
    }
    
    if (ratesToInsert.length > 0) {
      const { error } = await supabase
        .from('exchange_rates')
        .insert(ratesToInsert);
      
      if (error) {
        logger.error('Failed to insert exchange rates', { error: error.message });
        throw error;
      }
      
      logger.info('Exchange rates updated successfully', {
        ratesInserted: ratesToInsert.length,
        date: today
      });
    } else {
      logger.info('Exchange rates already up to date', { date: today });
    }
    
  } catch (error) {
    logger.error('Exchange rate update job failed', {
      error: error.message,
      stack: error.stack
    });
  }
}

/**
 * Check for upcoming subscription renewals and send notifications
 */
async function checkUpcomingRenewals() {
  try {
    logger.info('Starting upcoming renewals check job');
    
    const today = new Date();
    const threeDaysFromNow = new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000);
    const sevenDaysFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    const todayStr = today.toISOString().split('T')[0];
    const threeDaysStr = threeDaysFromNow.toISOString().split('T')[0];
    const sevenDaysStr = sevenDaysFromNow.toISOString().split('T')[0];
    
    // Get subscriptions with upcoming renewals
    const { data: upcomingRenewals, error } = await supabase
      .from('subscriptions')
      .select(`
        id,
        name,
        amount,
        currency,
        billing_cycle,
        next_billing_date,
        businesses!inner(
          id,
          name,
          owner_id,
          profiles!inner(
            id,
            email,
            full_name
          )
        )
      `)
      .eq('status', 'active')
      .or(`next_billing_date.eq.${todayStr},next_billing_date.eq.${threeDaysStr},next_billing_date.eq.${sevenDaysStr}`);
    
    if (error) {
      logger.error('Failed to fetch upcoming renewals', { error: error.message });
      throw error;
    }
    
    if (!upcomingRenewals || upcomingRenewals.length === 0) {
      logger.info('No upcoming renewals found');
      return;
    }
    
    // Group renewals by user and urgency
    const renewalsByUser = {};
    
    upcomingRenewals.forEach(subscription => {
      const userId = subscription.businesses.owner_id;
      const userEmail = subscription.businesses.profiles.email;
      const userName = subscription.businesses.profiles.full_name;
      
      if (!renewalsByUser[userId]) {
        renewalsByUser[userId] = {
          email: userEmail,
          name: userName,
          today: [],
          threeDays: [],
          sevenDays: []
        };
      }
      
      if (subscription.next_billing_date === todayStr) {
        renewalsByUser[userId].today.push(subscription);
      } else if (subscription.next_billing_date === threeDaysStr) {
        renewalsByUser[userId].threeDays.push(subscription);
      } else if (subscription.next_billing_date === sevenDaysStr) {
        renewalsByUser[userId].sevenDays.push(subscription);
      }
    });
    
    // In a real application, you would send emails here
    // For now, we'll just log the notifications
    for (const [userId, userRenewals] of Object.entries(renewalsByUser)) {
      if (userRenewals.today.length > 0) {
        logger.info('Subscription renewal due today', {
          userId,
          userEmail: userRenewals.email,
          subscriptions: userRenewals.today.map(s => ({ id: s.id, name: s.name, amount: s.amount }))
        });
      }
      
      if (userRenewals.threeDays.length > 0) {
        logger.info('Subscription renewal due in 3 days', {
          userId,
          userEmail: userRenewals.email,
          subscriptions: userRenewals.threeDays.map(s => ({ id: s.id, name: s.name, amount: s.amount }))
        });
      }
      
      if (userRenewals.sevenDays.length > 0) {
        logger.info('Subscription renewal due in 7 days', {
          userId,
          userEmail: userRenewals.email,
          subscriptions: userRenewals.sevenDays.map(s => ({ id: s.id, name: s.name, amount: s.amount }))
        });
      }
    }
    
    logger.info('Upcoming renewals check completed', {
      totalRenewals: upcomingRenewals.length,
      usersNotified: Object.keys(renewalsByUser).length
    });
    
  } catch (error) {
    logger.error('Upcoming renewals check job failed', {
      error: error.message,
      stack: error.stack
    });
  }
}

/**
 * Update next billing dates for subscriptions
 */
async function updateNextBillingDates() {
  try {
    logger.info('Starting next billing dates update job');
    
    const today = new Date().toISOString().split('T')[0];
    
    // Get subscriptions that need billing date updates
    const { data: subscriptions, error } = await supabase
      .from('subscriptions')
      .select('id, billing_cycle, next_billing_date')
      .eq('status', 'active')
      .lte('next_billing_date', today);
    
    if (error) {
      logger.error('Failed to fetch subscriptions for billing update', { error: error.message });
      throw error;
    }
    
    if (!subscriptions || subscriptions.length === 0) {
      logger.info('No subscriptions need billing date updates');
      return;
    }
    
    const updates = [];
    
    subscriptions.forEach(subscription => {
      const currentDate = new Date(subscription.next_billing_date);
      let nextDate;
      
      switch (subscription.billing_cycle) {
        case 'weekly':
          nextDate = new Date(currentDate.getTime() + 7 * 24 * 60 * 60 * 1000);
          break;
        case 'monthly':
          nextDate = new Date(currentDate);
          nextDate.setMonth(nextDate.getMonth() + 1);
          break;
        case 'quarterly':
          nextDate = new Date(currentDate);
          nextDate.setMonth(nextDate.getMonth() + 3);
          break;
        case 'yearly':
          nextDate = new Date(currentDate);
          nextDate.setFullYear(nextDate.getFullYear() + 1);
          break;
        default:
          // Default to monthly if billing cycle is unknown
          nextDate = new Date(currentDate);
          nextDate.setMonth(nextDate.getMonth() + 1);
      }
      
      updates.push({
        id: subscription.id,
        next_billing_date: nextDate.toISOString().split('T')[0]
      });
    });
    
    // Update billing dates in batches
    const batchSize = 100;
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      
      for (const update of batch) {
        const { error: updateError } = await supabase
          .from('subscriptions')
          .update({ next_billing_date: update.next_billing_date })
          .eq('id', update.id);
        
        if (updateError) {
          logger.error('Failed to update subscription billing date', {
            subscriptionId: update.id,
            error: updateError.message
          });
        }
      }
    }
    
    logger.info('Next billing dates updated successfully', {
      subscriptionsUpdated: updates.length
    });
    
  } catch (error) {
    logger.error('Next billing dates update job failed', {
      error: error.message,
      stack: error.stack
    });
  }
}

/**
 * Clean up old exchange rates (keep only last 90 days)
 */
async function cleanupOldExchangeRates() {
  try {
    logger.info('Starting exchange rates cleanup job');
    
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
    const cutoffDate = ninetyDaysAgo.toISOString().split('T')[0];
    
    const { error } = await supabase
      .from('exchange_rates')
      .delete()
      .lt('date', cutoffDate);
    
    if (error) {
      logger.error('Failed to cleanup old exchange rates', { error: error.message });
      throw error;
    }
    
    logger.info('Old exchange rates cleaned up successfully', {
      cutoffDate
    });
    
  } catch (error) {
    logger.error('Exchange rates cleanup job failed', {
      error: error.message,
      stack: error.stack
    });
  }
}

/**
 * Initialize and start all cron jobs
 */
function initializeCronJobs() {
  logger.info('Initializing cron jobs');
  
  // Update exchange rates daily at 2:00 AM
  cron.schedule('0 2 * * *', fetchExchangeRates, {
    scheduled: true,
    timezone: 'Asia/Kuwait'
  });
  
  // Check upcoming renewals daily at 9:00 AM
  cron.schedule('0 9 * * *', checkUpcomingRenewals, {
    scheduled: true,
    timezone: 'Asia/Kuwait'
  });
  
  // Update next billing dates daily at 1:00 AM
  cron.schedule('0 1 * * *', updateNextBillingDates, {
    scheduled: true,
    timezone: 'Asia/Kuwait'
  });
  
  // Cleanup old exchange rates weekly on Sunday at 3:00 AM
  cron.schedule('0 3 * * 0', cleanupOldExchangeRates, {
    scheduled: true,
    timezone: 'Asia/Kuwait'
  });
  
  logger.info('Cron jobs initialized successfully');
  
  // Run initial exchange rate fetch if no rates exist for today
  setTimeout(async () => {
    const today = new Date().toISOString().split('T')[0];
    const { data: existingRates } = await supabase
      .from('exchange_rates')
      .select('id')
      .eq('date', today)
      .limit(1);
    
    if (!existingRates || existingRates.length === 0) {
      logger.info('No exchange rates found for today, running initial fetch');
      await fetchExchangeRates();
    }
  }, 5000); // Wait 5 seconds after server start
}

/**
 * Stop all cron jobs
 */
function stopCronJobs() {
  logger.info('Stopping all cron jobs');
  cron.getTasks().forEach(task => {
    task.stop();
  });
}

/**
 * Manual trigger functions for testing
 */
const manualTriggers = {
  fetchExchangeRates,
  checkUpcomingRenewals,
  updateNextBillingDates,
  cleanupOldExchangeRates
};

module.exports = {
  initializeCronJobs,
  stopCronJobs,
  manualTriggers
};