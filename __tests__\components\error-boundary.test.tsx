import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { E<PERSON>rBoundary, useErrorHand<PERSON>, useAsyncErrorHandler } from '@/components/error-boundary'

// Component that throws an error for testing
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error')
  }
  return <div>No error</div>
}

// Component that uses useErrorH<PERSON>ler hook
const TestErrorHandler = () => {
  const handleError = useErrorHandler()
  
  const triggerError = () => {
    handleError(new Error('Hook error'), { componentStack: 'TestComponent' })
  }
  
  return <button onClick={triggerError}>Trigger Error</button>
}

// Component that uses useAsyncErrorHandler hook
const TestAsyncErrorHandler = () => {
  const { executeWithRetry } = useAsyncErrorHandler()
  
  const triggerAsyncError = async () => {
    await executeWithRetry(async () => {
      throw new Error('Async error')
    }, 1, 100)
  }
  
  return <button onClick={triggerAsyncError}>Trigger Async Error</button>
}

describe('ErrorBoundary', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Suppress console.error for these tests
    jest.spyOn(console, 'error').mockImplementation(() => {})
    jest.spyOn(console, 'group').mockImplementation(() => {})
    jest.spyOn(console, 'groupEnd').mockImplementation(() => {})
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  it('should render children when there is no error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    )

    expect(screen.getByText('No error')).toBeInTheDocument()
  })

  it('should render error fallback when child component throws', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )

    expect(screen.getByText('Something went wrong')).toBeInTheDocument()
    expect(screen.getByText('An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.')).toBeInTheDocument()
  })

  it('should show error details when expanded', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )

    const detailsElement = screen.getByText('Error Details')
    fireEvent.click(detailsElement)

    expect(screen.getByText('Test error')).toBeInTheDocument()
  })

  it('should have Try Again and Reload Page buttons', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )

    expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /reload page/i })).toBeInTheDocument()
  })

  it('should reset error when Try Again is clicked', () => {
    const { rerender } = render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )

    expect(screen.getByText('Something went wrong')).toBeInTheDocument()

    const tryAgainButton = screen.getByRole('button', { name: /try again/i })
    fireEvent.click(tryAgainButton)

    // Re-render with no error
    rerender(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    )

    expect(screen.getByText('No error')).toBeInTheDocument()
  })

  it('should use custom fallback component when provided', () => {
    const CustomFallback = ({ error, resetError }: any) => (
      <div>
        <p>Custom error: {error?.message}</p>
        <button onClick={resetError}>Custom Reset</button>
      </div>
    )

    render(
      <ErrorBoundary fallback={CustomFallback}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )

    expect(screen.getByText('Custom error: Test error')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /custom reset/i })).toBeInTheDocument()
  })
})

describe('useErrorHandler', () => {
  beforeEach(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {})
    jest.spyOn(console, 'group').mockImplementation(() => {})
    jest.spyOn(console, 'groupEnd').mockImplementation(() => {})
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  it('should log errors when triggered', () => {
    render(<TestErrorHandler />)

    const button = screen.getByRole('button', { name: /trigger error/i })
    fireEvent.click(button)

    expect(console.error).toHaveBeenCalledWith('Error caught by hook:', expect.any(Error), expect.any(Object))
  })
})

describe('useAsyncErrorHandler', () => {
  beforeEach(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {})
    jest.spyOn(console, 'group').mockImplementation(() => {})
    jest.spyOn(console, 'groupEnd').mockImplementation(() => {})
    jest.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  it('should handle async errors with retry logic', async () => {
    let attempts = 0
    const { executeWithRetry } = useAsyncErrorHandler()

    const failingOperation = async () => {
      attempts++
      if (attempts < 3) {
        throw new Error('Async operation failed')
      }
      return 'success'
    }

    const result = await executeWithRetry(failingOperation, 3, 10)
    expect(result).toBe('success')
    expect(attempts).toBe(3)
  })

  it('should throw error after max retries', async () => {
    const { executeWithRetry } = useAsyncErrorHandler()

    const alwaysFailingOperation = async () => {
      throw new Error('Always fails')
    }

    await expect(
      executeWithRetry(alwaysFailingOperation, 2, 10)
    ).rejects.toThrow('Always fails')
  })
})