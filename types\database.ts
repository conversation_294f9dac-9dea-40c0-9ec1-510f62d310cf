export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          first_name: string | null
          last_name: string | null
          phone: string | null
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          first_name?: string | null
          last_name?: string | null
          phone?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          first_name?: string | null
          last_name?: string | null
          phone?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      businesses: {
        Row: {
          id: string
          owner_id: string
          name: string
          description: string | null
          industry: string | null
          website: string | null
          phone: string | null
          email: string | null
          address: any | null
          city: string | null
          country: string
          currency: string
          settings: any | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          owner_id: string
          name: string
          description?: string | null
          industry?: string | null
          website?: string | null
          phone?: string | null
          email?: string | null
          address?: any | null
          city?: string | null
          country?: string
          currency?: string
          settings?: any | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          owner_id?: string
          name?: string
          description?: string | null
          industry?: string | null
          website?: string | null
          phone?: string | null
          email?: string | null
          address?: any | null
          city?: string | null
          country?: string
          currency?: string
          settings?: any | null
          created_at?: string
          updated_at?: string
        }
      }
      subscriptions: {
        Row: {
          id: string
          business_id: string
          name: string
          description: string | null
          amount: number
          currency: string
          billing_cycle: 'weekly' | 'monthly' | 'quarterly' | 'yearly'
          start_date: string
          end_date: string | null
          next_billing_date: string
          status: 'active' | 'paused' | 'cancelled'
          category: string | null
          vendor: string | null
          website: string | null
          payment_method: string | null
          auto_renew: boolean
          notes: string | null
          tags: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          business_id: string
          name: string
          description?: string | null
          amount: number
          currency: string
          billing_cycle: 'weekly' | 'monthly' | 'quarterly' | 'yearly'
          start_date: string
          end_date?: string | null
          next_billing_date: string
          status?: 'active' | 'paused' | 'cancelled'
          category?: string | null
          vendor?: string | null
          website?: string | null
          payment_method?: string | null
          auto_renew?: boolean
          notes?: string | null
          tags?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          business_id?: string
          name?: string
          description?: string | null
          amount?: number
          currency?: string
          billing_cycle?: 'weekly' | 'monthly' | 'quarterly' | 'yearly'
          start_date?: string
          end_date?: string | null
          next_billing_date?: string
          status?: 'active' | 'paused' | 'cancelled'
          category?: string | null
          vendor?: string | null
          website?: string | null
          payment_method?: string | null
          auto_renew?: boolean
          notes?: string | null
          tags?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          business_id: string
          subscription_id: string | null
          amount: number
          currency: string
          kwd_amount: number | null
          exchange_rate: number | null
          payment_date: string
          payment_method: string | null
          status: 'pending' | 'completed' | 'failed' | 'refunded'
          description: string | null
          reference_number: string | null
          receipt_url: string | null
          conversion_timestamp: string | null
          auto_create_reimbursement: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          business_id: string
          subscription_id?: string | null
          amount: number
          currency: string
          kwd_amount?: number | null
          exchange_rate?: number | null
          payment_date: string
          payment_method?: string | null
          status?: 'pending' | 'completed' | 'failed' | 'refunded'
          description?: string | null
          reference_number?: string | null
          receipt_url?: string | null
          conversion_timestamp?: string | null
          auto_create_reimbursement?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          business_id?: string
          subscription_id?: string | null
          amount?: number
          currency?: string
          kwd_amount?: number | null
          exchange_rate?: number | null
          payment_date?: string
          payment_method?: string | null
          status?: 'pending' | 'completed' | 'failed' | 'refunded'
          description?: string | null
          reference_number?: string | null
          receipt_url?: string | null
          conversion_timestamp?: string | null
          auto_create_reimbursement?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      reimbursements: {
        Row: {
          id: string
          business_id: string
          payment_id: string | null
          amount: number
          currency: string
          description: string
          category: string | null
          receipt_url: string | null
          status: 'pending' | 'approved' | 'rejected' | 'paid'
          submitted_date: string | null
          approved_date: string | null
          paid_date: string | null
          notes: string | null
          approved_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          business_id: string
          payment_id?: string | null
          amount: number
          currency?: string
          description: string
          category?: string | null
          receipt_url?: string | null
          status?: 'pending' | 'approved' | 'rejected' | 'paid'
          submitted_date?: string | null
          approved_date?: string | null
          paid_date?: string | null
          notes?: string | null
          approved_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          business_id?: string
          payment_id?: string | null
          amount?: number
          currency?: string
          description?: string
          category?: string | null
          receipt_url?: string | null
          status?: 'pending' | 'approved' | 'rejected' | 'paid'
          submitted_date?: string | null
          approved_date?: string | null
          paid_date?: string | null
          notes?: string | null
          approved_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      exchange_rates: {
        Row: {
          id: string
          from_currency: string
          to_currency: string
          rate: number
          date: string
          source: string | null
          created_at: string
        }
        Insert: {
          id?: string
          from_currency: string
          to_currency: string
          rate: number
          date: string
          source?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          from_currency?: string
          to_currency?: string
          rate?: number
          date?: string
          source?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}